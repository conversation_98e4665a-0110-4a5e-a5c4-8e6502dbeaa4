# PlayerServerTP - 跨服传送插件

一个功能完整的Minecraft跨服传送插件，支持Velocity代理和Paper服务器。

## 🌟 特性

### 核心功能
- 🚀 **跨服传送** - 在不同服务器间快速传送玩家
- 🎯 **智能指令** - 简洁的 `/stp` 指令系统
- 🛡️ **权限管理** - 细粒度的权限控制
- ⏰ **冷却时间** - 防止传送滥用
- 🔒 **安全检查** - 智能的安全位置检测
- 🎨 **传送特效** - 美观的粒子和声音效果

### 高级功能
- 📡 **Plugin Messaging** - 高效的跨服通信
- 🔗 **插件兼容** - 与主流插件良好集成
- 🌍 **多语言支持** - 可自定义的消息系统
- 📊 **调试工具** - 完善的调试和诊断功能
- ⚙️ **热重载** - 无需重启即可更新配置

## 📋 系统要求

- **Velocity**: 3.4.0+
- **Paper**: 1.21+
- **Java**: 17+ (Velocity) / 21+ (Paper)

## 🚀 快速开始

### 1. 安装插件

**Velocity端:**
1. 将 `PlayerServerTPVelocity-1.0-SNAPSHOT.jar` 放入 `plugins` 文件夹
2. 重启Velocity代理

**Paper端:**
1. 将 `PlayerServerTPPaper-1.0-SNAPSHOT.jar` 放入每个Paper服务器的 `plugins` 文件夹
2. 重启所有Paper服务器

### 2. 配置插件

**Velocity配置 (`plugins/PlayerServerTPVelocity/config.yml`):**
```yaml
servers:
  enabled-servers:
    - "lobby"
    - "survival"
    - "creative"
  aliases:
    "hub": "lobby"
    "surv": "survival"

settings:
  cooldown-time: 5
  debug-mode: false
```

**Paper配置 (`plugins/PlayerServerTPPaper/config.yml`):**
```yaml
safety:
  check-safe-location: true
  find-safe-location: true
  search-radius: 5

effects:
  enable-effects: true
```

### 3. 设置权限

```yaml
# 基础权限
playerservertp.teleport.self    # 传送自己
playerservertp.teleport.others  # 传送他人
playerservertp.list            # 查看服务器列表
playerservertp.bypass.cooldown # 绕过冷却时间
playerservertp.admin           # 管理员权限
```

## 🎮 使用方法

### 基础指令

```
/stp help                    # 显示帮助信息
/stp list                    # 查看可用服务器列表
/stp <服务器>                # 传送到指定服务器
/stp <玩家> <服务器>         # 传送玩家到指定服务器
/stp reload                  # 重载配置文件 (管理员)
```

### 使用示例

```
/stp lobby                   # 传送到大厅服务器
/stp hub                     # 使用别名传送到大厅
/stp Steve survival          # 传送Steve到生存服务器
/stp list                    # 查看所有可用服务器
```

## ⚙️ 配置详解

### Velocity端配置

```yaml
# 基础设置
settings:
  debug-mode: false          # 调试模式
  cooldown-time: 5           # 冷却时间(秒)
  safe-teleport: true        # 安全传送

# 服务器配置
servers:
  enabled-servers:           # 启用的服务器列表
    - "lobby"
    - "survival"
  aliases:                   # 服务器别名
    "hub": "lobby"
  display-names:             # 显示名称
    "lobby": "大厅服务器"

# 权限设置
permissions:
  enable-permission-check: true
  default-permissions:
    - "playerservertp.teleport.self"

# 消息配置
messages:
  teleport-success: "&a成功传送到服务器 &e{server}&a！"
  player-not-found: "&c玩家 &e{player} &c不存在或不在线！"
  # ... 更多消息配置
```

### Paper端配置

```yaml
# 安全传送设置
safety:
  check-safe-location: true  # 检查位置安全性
  find-safe-location: true   # 自动寻找安全位置
  search-radius: 5           # 搜索半径
  unsafe-blocks:             # 危险方块列表
    - "LAVA"
    - "FIRE"

# 传送特效
effects:
  enable-effects: true
  before-teleport:
    particles:
      type: "PORTAL"
      count: 20
    sound:
      type: "ENTITY_ENDERMAN_TELEPORT"

# 兼容性设置
compatibility:
  enable-plugin-hooks: true
  supported-plugins:
    - "WorldGuard"
    - "GriefPrevention"
```

## 🔧 开发信息

### 项目结构

```
PlayerServerTP/
├── PlayerServerTPVelocity/     # Velocity端插件
│   ├── commands/               # 指令处理
│   ├── managers/               # 管理器类
│   ├── messaging/              # 消息系统
│   ├── protocol/               # 通信协议
│   └── utils/                  # 工具类
└── PlayerServerTPPaper/        # Paper端插件
    ├── handlers/               # 处理器类
    ├── executors/              # 执行器类
    ├── safety/                 # 安全检查
    ├── compatibility/          # 兼容性处理
    └── messaging/              # 消息系统
```

### 通信协议

插件使用Plugin Messaging进行跨服通信：

```
TELEPORT_PLAYER|玩家名|目标服务器|发起者
TELEPORT_RESPONSE|玩家名|状态|消息|发起者
GET_SERVERS|请求者
SERVER_LIST|服务器1,服务器2,服务器3
```

## 🐛 故障排除

### 常见问题

1. **传送失败**
   - 检查目标服务器是否在线
   - 确认服务器名称正确
   - 检查权限配置

2. **Plugin Message不工作**
   - 确保至少有一个玩家在线
   - 检查BungeeCord通道注册
   - 查看调试日志

3. **权限问题**
   - 检查权限插件配置
   - 确认权限节点正确
   - 使用 `/stp help` 查看权限信息

### 调试模式

启用调试模式获取详细日志：
```yaml
settings:
  debug-mode: true
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请：
1. 查看本文档的故障排除部分
2. 在GitHub上提交Issue
3. 加入我们的Discord服务器

---

**PlayerServerTP** - 让跨服传送变得简单高效！ 🚀
