package org.PlayerServerTPpaper.playerServerTPVelocity.messaging;

import com.google.common.io.ByteArrayDataOutput;
import com.google.common.io.ByteStreams;
import com.velocitypowered.api.proxy.Player;
import com.velocitypowered.api.proxy.ProxyServer;
import com.velocitypowered.api.proxy.server.RegisteredServer;
import com.velocitypowered.api.proxy.messages.MinecraftChannelIdentifier;
import org.PlayerServerTPpaper.playerServerTPVelocity.config.ConfigManager;
import org.PlayerServerTPpaper.playerServerTPVelocity.protocol.MessageType;
import org.PlayerServerTPpaper.playerServerTPVelocity.protocol.ProtocolConstants;
import org.slf4j.Logger;

import java.util.Optional;

/**
 * Plugin Messaging 消息发送器
 * 负责向Paper服务器发送跨服传送相关的消息
 */
public class MessageSender {
    
    private final ProxyServer proxy;
    private final ConfigManager configManager;
    private final Logger logger;
    
    public MessageSender(ProxyServer proxy, ConfigManager configManager, Logger logger) {
        this.proxy = proxy;
        this.configManager = configManager;
        this.logger = logger;
    }
    
    /**
     * 发送传送玩家消息到目标服务器（如果有Paper端插件）
     * 如果没有Paper端插件，则直接使用Velocity传送
     * @param targetPlayer 要传送的玩家
     * @param targetServer 目标服务器
     * @param initiator 发起传送的玩家（可以是自己或管理员）
     * @return 是否发送成功
     */
    public boolean sendTeleportPlayerMessage(String targetPlayer, String targetServer, String initiator) {
        // 首先尝试直接传送（Velocity原生功能）
        boolean directTeleportSuccess = performDirectTeleport(targetPlayer, targetServer);

        if (directTeleportSuccess) {
            logger.info("直接传送成功：玩家 {} 到服务器 {} (发起者: {})",
                targetPlayer, targetServer, initiator);

            // 如果直接传送成功，尝试发送增强消息到Paper端（可选）
            tryEnhancedTeleport(targetPlayer, targetServer, initiator);
            return true;
        } else {
            logger.warn("直接传送失败：玩家 {} 到服务器 {} (发起者: {})",
                targetPlayer, targetServer, initiator);
            return false;
        }
    }

    /**
     * 执行直接传送（使用Velocity原生API）
     * @param targetPlayerName 目标玩家名称
     * @param targetServerName 目标服务器名称
     * @return 是否传送成功
     */
    private boolean performDirectTeleport(String targetPlayerName, String targetServerName) {
        try {
            // 获取目标玩家
            Optional<Player> targetPlayer = proxy.getPlayer(targetPlayerName);
            if (!targetPlayer.isPresent()) {
                logger.warn("玩家 {} 不在线，无法传送", targetPlayerName);
                return false;
            }

            // 获取目标服务器
            Optional<RegisteredServer> targetServer = proxy.getServer(targetServerName);
            if (!targetServer.isPresent()) {
                logger.warn("服务器 {} 不存在，无法传送", targetServerName);
                return false;
            }

            // 检查玩家是否已经在目标服务器
            Optional<RegisteredServer> currentServer = targetPlayer.get().getCurrentServer()
                .map(connection -> connection.getServer());

            if (currentServer.isPresent() &&
                currentServer.get().getServerInfo().getName().equals(targetServerName)) {
                logger.info("玩家 {} 已经在服务器 {} 上", targetPlayerName, targetServerName);
                return true; // 认为是成功的
            }

            // 执行传送
            targetPlayer.get().createConnectionRequest(targetServer.get()).fireAndForget();

            logger.info("Velocity直接传送：玩家 {} -> 服务器 {}", targetPlayerName, targetServerName);
            return true;

        } catch (Exception e) {
            logger.error("执行直接传送时发生错误", e);
            return false;
        }
    }

    /**
     * 尝试发送增强传送消息到Paper端（可选功能）
     * @param targetPlayer 目标玩家
     * @param targetServer 目标服务器
     * @param initiator 发起者
     */
    private void tryEnhancedTeleport(String targetPlayer, String targetServer, String initiator) {
        try {
            // 构建增强消息内容
            String message = String.join(ProtocolConstants.MESSAGE_SEPARATOR,
                MessageType.TELEPORT_PLAYER.getIdentifier(),
                targetPlayer,
                targetServer,
                initiator
            );

            // 尝试发送到目标服务器（如果有Paper端插件会处理，没有则忽略）
            boolean success = sendMessageToServer(targetServer, message);

            if (success) {
                logger.debug("已发送增强传送消息到服务器 {}", targetServer);
            } else {
                logger.debug("无法发送增强传送消息到服务器 {}（可能没有Paper端插件）", targetServer);
            }

        } catch (Exception e) {
            logger.debug("发送增强传送消息时发生错误（这是正常的，如果目标服务器没有Paper端插件）", e);
        }
    }
    
    /**
     * 发送获取服务器列表消息
     * @param requester 请求者
     * @param targetServer 目标服务器
     * @return 是否发送成功
     */
    public boolean sendGetServersMessage(String requester, String targetServer) {
        String message = String.join(ProtocolConstants.MESSAGE_SEPARATOR,
            MessageType.GET_SERVERS.getIdentifier(),
            requester
        );
        
        return sendMessageToServer(targetServer, message);
    }
    
    /**
     * 发送检查玩家状态消息
     * @param targetPlayer 要检查的玩家
     * @param targetServer 目标服务器
     * @param requester 请求者
     * @return 是否发送成功
     */
    public boolean sendCheckPlayerMessage(String targetPlayer, String targetServer, String requester) {
        String message = String.join(ProtocolConstants.MESSAGE_SEPARATOR,
            MessageType.CHECK_PLAYER.getIdentifier(),
            targetPlayer,
            requester
        );
        
        return sendMessageToServer(targetServer, message);
    }
    
    /**
     * 向指定服务器发送Plugin Message
     * @param serverName 服务器名称
     * @param message 消息内容
     * @return 是否发送成功
     */
    private boolean sendMessageToServer(String serverName, String message) {
        // 获取目标服务器
        Optional<RegisteredServer> server = proxy.getServer(serverName);
        if (!server.isPresent()) {
            logger.warn("无法找到服务器: {}", serverName);
            return false;
        }
        
        // 需要一个在线玩家来发送消息
        Optional<Player> onlinePlayer = findOnlinePlayer();
        if (!onlinePlayer.isPresent()) {
            logger.warn("没有在线玩家，无法发送Plugin Message");
            return false;
        }
        
        try {
            // 直接发送消息（不再使用BungeeCord Forward格式）
            ByteArrayDataOutput out = ByteStreams.newDataOutput();
            out.writeUTF(message);

            // 发送消息
            MinecraftChannelIdentifier channel = MinecraftChannelIdentifier.from(ProtocolConstants.CHANNEL_NAME);
            onlinePlayer.get().sendPluginMessage(channel, out.toByteArray());
            
            // 调试日志
            if (configManager.getBoolean("settings.debug-mode", false)) {
                logger.debug("发送Plugin Message到服务器 {}: {}", serverName, message);
            }
            
            return true;
            
        } catch (Exception e) {
            logger.error("发送Plugin Message时发生错误", e);
            return false;
        }
    }
    
    /**
     * 发送自定义消息到所有Paper服务器
     * @param message 消息内容
     * @return 是否发送成功
     */
    public boolean sendCustomMessage(String message) {
        Optional<Player> onlinePlayer = findOnlinePlayer();
        if (!onlinePlayer.isPresent()) {
            logger.warn("没有在线玩家，无法发送Plugin Message");
            return false;
        }

        try {
            ByteArrayDataOutput out = ByteStreams.newDataOutput();
            out.writeUTF(message);

            MinecraftChannelIdentifier channel = MinecraftChannelIdentifier.from(ProtocolConstants.CHANNEL_NAME);
            onlinePlayer.get().sendPluginMessage(channel, out.toByteArray());

            logger.debug("发送自定义消息: {}", message);
            return true;

        } catch (Exception e) {
            logger.error("发送自定义消息时发生错误", e);
            return false;
        }
    }

    /**
     * 向所有服务器广播消息
     * @param message 消息内容
     * @return 成功发送的服务器数量
     */
    public int broadcastMessage(String message) {
        int successCount = 0;
        
        for (RegisteredServer server : proxy.getAllServers()) {
            if (sendMessageToServer(server.getServerInfo().getName(), message)) {
                successCount++;
            }
        }
        
        logger.info("广播消息到 {} 个服务器，成功 {} 个", proxy.getAllServers().size(), successCount);
        return successCount;
    }
    
    /**
     * 向特定玩家所在的服务器发送消息
     * @param playerName 玩家名称
     * @param message 消息内容
     * @return 是否发送成功
     */
    public boolean sendMessageToPlayerServer(String playerName, String message) {
        Optional<Player> player = proxy.getPlayer(playerName);
        if (!player.isPresent()) {
            logger.warn("玩家 {} 不在线，无法发送消息", playerName);
            return false;
        }
        
        Optional<RegisteredServer> currentServer = player.get().getCurrentServer()
            .map(connection -> connection.getServer());
        
        if (!currentServer.isPresent()) {
            logger.warn("无法确定玩家 {} 所在的服务器", playerName);
            return false;
        }
        
        return sendMessageToServer(currentServer.get().getServerInfo().getName(), message);
    }
    
    /**
     * 查找一个在线玩家用于发送Plugin Message
     * @return 在线玩家（如果有的话）
     */
    private Optional<Player> findOnlinePlayer() {
        return proxy.getAllPlayers().stream().findFirst();
    }
    
    /**
     * 发送传送响应消息（从Paper端接收到响应后转发给请求者）
     * @param playerName 玩家名称
     * @param status 传送状态
     * @param message 响应消息
     * @param requester 原始请求者
     * @return 是否发送成功
     */
    public boolean sendTeleportResponse(String playerName, String status, String message, String requester) {
        Optional<Player> requesterPlayer = proxy.getPlayer(requester);
        if (!requesterPlayer.isPresent()) {
            logger.warn("请求者 {} 不在线，无法发送响应", requester);
            return false;
        }
        
        // 直接向请求者发送消息（不需要通过Plugin Messaging）
        String responseMessage = String.format("传送结果 - 玩家: %s, 状态: %s, 消息: %s", 
            playerName, status, message);
        
        // 这里可以根据状态发送不同颜色的消息
        if (ProtocolConstants.TeleportStatus.SUCCESS.equals(status)) {
            requesterPlayer.get().sendMessage(
                net.kyori.adventure.text.Component.text(responseMessage)
                    .color(net.kyori.adventure.text.format.NamedTextColor.GREEN)
            );
        } else {
            requesterPlayer.get().sendMessage(
                net.kyori.adventure.text.Component.text(responseMessage)
                    .color(net.kyori.adventure.text.format.NamedTextColor.RED)
            );
        }
        
        return true;
    }
    
    /**
     * 获取消息发送统计信息
     * @return 统计信息
     */
    public MessageStats getMessageStats() {
        int totalServers = proxy.getAllServers().size();
        int onlinePlayers = proxy.getPlayerCount();
        boolean canSendMessages = onlinePlayers > 0;
        
        return new MessageStats(totalServers, onlinePlayers, canSendMessages);
    }
    
    /**
     * 消息统计信息类
     */
    public static class MessageStats {
        private final int totalServers;
        private final int onlinePlayers;
        private final boolean canSendMessages;
        
        public MessageStats(int totalServers, int onlinePlayers, boolean canSendMessages) {
            this.totalServers = totalServers;
            this.onlinePlayers = onlinePlayers;
            this.canSendMessages = canSendMessages;
        }
        
        public int getTotalServers() {
            return totalServers;
        }
        
        public int getOnlinePlayers() {
            return onlinePlayers;
        }
        
        public boolean canSendMessages() {
            return canSendMessages;
        }
    }
}
