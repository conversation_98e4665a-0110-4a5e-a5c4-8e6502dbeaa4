package org.PlayerServerTPpaper.playerServerTPVelocity.commands;

import com.velocitypowered.api.command.CommandSource;
import com.velocitypowered.api.command.SimpleCommand;
import com.velocitypowered.api.proxy.Player;
import com.velocitypowered.api.proxy.ProxyServer;
import com.velocitypowered.api.proxy.server.RegisteredServer;
import org.PlayerServerTPpaper.playerServerTPVelocity.PlayerServerTPVelocity;
import org.PlayerServerTPpaper.playerServerTPVelocity.config.ConfigManager;
import org.PlayerServerTPpaper.playerServerTPVelocity.protocol.ProtocolConstants;
import org.PlayerServerTPpaper.playerServerTPVelocity.utils.MessageUtils;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * 主要的跨服传送指令处理类
 * 处理 /stp 指令的各种用法
 */
public class ServerTeleportCommand implements SimpleCommand {
    
    private final PlayerServerTPVelocity plugin;
    private final ProxyServer proxy;
    private final ConfigManager configManager;
    private final Logger logger;
    
    public ServerTeleportCommand(PlayerServerTPVelocity plugin, ProxyServer proxy, 
                               ConfigManager configManager, Logger logger) {
        this.plugin = plugin;
        this.proxy = proxy;
        this.configManager = configManager;
        this.logger = logger;
    }
    
    @Override
    public void execute(Invocation invocation) {
        CommandSource source = invocation.source();
        String[] args = invocation.arguments();
        
        // 检查是否启用调试模式
        boolean debugMode = configManager.getBoolean("settings.debug-mode", false);
        
        if (debugMode) {
            logger.info("玩家 {} 执行了指令: /stp {}", 
                source instanceof Player ? ((Player) source).getUsername() : "控制台", 
                String.join(" ", args));
        }
        
        // 处理不同的指令用法
        if (args.length == 0) {
            // /stp - 显示帮助信息
            showHelp(source);
            return;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "help":
            case "帮助":
                showHelp(source);
                break;

            case "list":
            case "列表":
                showServerList(source);
                break;

            case "reload":
            case "重载":
                handleReload(source);
                break;

            case "tp":
                // 处理自己传送
                handleSelfTeleport(source, args);
                break;

            case "tpto":
                // 处理传送他人
                handleOtherTeleport(source, args);
                break;

            case "getserverlist":
                // 处理获取服务器列表
                handleGetServerList(source);
                break;

            default:
                showHelp(source);
                break;
        }
    }
    
    /**
     * 显示帮助信息
     */
    private void showHelp(CommandSource source) {
        String header = configManager.getString("messages.help-header", "&6=== PlayerServerTP 帮助 ===");
        String helpStp = configManager.getString("messages.help-stp", "&e/stp tp <服务器> &7- 传送到指定服务器");
        String helpStpPlayer = configManager.getString("messages.help-stp-player", "&e/stp tpto <玩家> <服务器> &7- 传送玩家到指定服务器");
        String helpStpList = configManager.getString("messages.help-stp-list", "&e/stp list &7- 查看可用服务器列表");
        
        source.sendMessage(MessageUtils.colorize(header));
        source.sendMessage(MessageUtils.colorize(helpStp));
        
        // 只有有权限的玩家才能看到传送他人的帮助
        if (source.hasPermission(ProtocolConstants.Permissions.TELEPORT_OTHERS)) {
            source.sendMessage(MessageUtils.colorize(helpStpPlayer));
        }
        
        source.sendMessage(MessageUtils.colorize(helpStpList));
        
        // 管理员权限的额外帮助
        if (source.hasPermission(ProtocolConstants.Permissions.ADMIN)) {
            source.sendMessage(MessageUtils.colorize("&e/stp reload &7- 重载配置文件"));
            source.sendMessage(MessageUtils.colorize("&e/stp getserverlist &7- 获取代理服务器列表"));
        }
    }
    
    /**
     * 显示服务器列表
     */
    private void showServerList(CommandSource source) {
        // 检查权限
        if (!source.hasPermission(ProtocolConstants.Permissions.LIST_SERVERS)) {
            String noPermission = configManager.getString("messages.no-permission", "&c你没有权限执行此操作！");
            source.sendMessage(MessageUtils.colorize(noPermission));
            return;
        }
        
        List<String> enabledServers = configManager.getStringList("servers.enabled-servers", new ArrayList<>());
        
        if (enabledServers.isEmpty()) {
            String emptyList = configManager.getString("messages.server-list-empty", "&c没有可用的服务器！");
            source.sendMessage(MessageUtils.colorize(emptyList));
            return;
        }
        
        String header = configManager.getString("messages.server-list-header", "&6=== 可用服务器列表 ===");
        source.sendMessage(MessageUtils.colorize(header));
        
        for (String server : enabledServers) {
            String displayName = configManager.getString("servers.display-names." + server, server);
            String listItem = configManager.getString("messages.server-list-item", "&e{server} &7- &f{display-name}");
            
            String formattedItem = MessageUtils.replacePlaceholders(listItem, 
                "server", server, 
                "display-name", displayName);
            
            source.sendMessage(MessageUtils.colorize(formattedItem));
        }
    }
    
    /**
     * 处理重载配置指令
     */
    private void handleReload(CommandSource source) {
        if (!source.hasPermission(ProtocolConstants.Permissions.ADMIN)) {
            String noPermission = configManager.getString("messages.no-permission", "&c你没有权限执行此操作！");
            source.sendMessage(MessageUtils.colorize(noPermission));
            return;
        }
        
        try {
            configManager.reloadConfig();
            source.sendMessage(MessageUtils.success("配置文件重载成功！"));
            logger.info("配置文件已被 {} 重载", 
                source instanceof Player ? ((Player) source).getUsername() : "控制台");
        } catch (Exception e) {
            source.sendMessage(MessageUtils.error("配置文件重载失败：" + e.getMessage()));
            logger.error("重载配置文件时发生错误", e);
        }
    }

    /**
     * 处理获取服务器列表指令
     */
    private void handleGetServerList(CommandSource source) {
        if (!source.hasPermission(ProtocolConstants.Permissions.ADMIN)) {
            String noPermission = configManager.getString("messages.no-permission", "&c你没有权限执行此操作！");
            source.sendMessage(MessageUtils.colorize(noPermission));
            return;
        }

        try {
            // 获取Velocity代理中注册的所有服务器
            var allServers = proxy.getAllServers();

            if (allServers.isEmpty()) {
                source.sendMessage(MessageUtils.warning("代理中没有注册任何服务器"));
                return;
            }

            // 提取服务器名称
            java.util.List<String> serverNames = new java.util.ArrayList<>();
            for (var server : allServers) {
                serverNames.add(server.getServerInfo().getName());
            }

            // 发送服务器列表到请求的Paper服务器
            if (source instanceof Player) {
                Player player = (Player) source;
                String requester = player.getUsername();

                // 构建服务器列表字符串
                String serverListStr = String.join(",", serverNames);

                // 发送到Paper端
                boolean success = sendFullServerListToPaper(requester, serverListStr);

                if (success) {
                    source.sendMessage(MessageUtils.success("服务器列表已发送到Paper端"));
                    source.sendMessage(MessageUtils.info("找到 " + serverNames.size() + " 个服务器: " + String.join(", ", serverNames)));
                } else {
                    source.sendMessage(MessageUtils.error("发送服务器列表失败"));
                }
            } else {
                // 控制台直接显示
                source.sendMessage(MessageUtils.info("代理服务器列表 (" + serverNames.size() + " 个):"));
                for (String serverName : serverNames) {
                    source.sendMessage(MessageUtils.colorize("&f  - " + serverName));
                }
            }

            logger.info("服务器列表请求处理完成，共 {} 个服务器", serverNames.size());

        } catch (Exception e) {
            source.sendMessage(MessageUtils.error("获取服务器列表时发生错误：" + e.getMessage()));
            logger.error("获取服务器列表时发生错误", e);
        }
    }

    /**
     * 发送完整服务器列表到Paper端
     */
    private boolean sendFullServerListToPaper(String requester, String serverListStr) {
        try {
            // 构建消息
            String message = String.join(ProtocolConstants.MESSAGE_SEPARATOR,
                "FULL_SERVER_LIST",
                requester,
                serverListStr
            );

            // 发送到所有Paper服务器
            return plugin.getMessageSender().sendCustomMessage(message);

        } catch (Exception e) {
            logger.error("发送服务器列表到Paper端时发生错误", e);
            return false;
        }
    }

    /**
     * 处理自己传送
     */
    private void handleSelfTeleport(CommandSource source, String[] args) {
        if (args.length < 2) {
            source.sendMessage(MessageUtils.error("用法: /stp tp <服务器>"));
            return;
        }

        String targetServer = args[1];
        if (!(source instanceof Player)) {
            source.sendMessage(MessageUtils.error("控制台无法执行传送操作！"));
            return;
        }

        Player player = (Player) source;

        // 检查权限
        if (!plugin.getPermissionManager().canTeleportSelf(player)) {
            String noPermission = configManager.getString("messages.no-permission", "&c你没有权限执行此操作！");
            player.sendMessage(MessageUtils.colorize(noPermission));
            return;
        }

        // 验证服务器
        var serverValidation = plugin.getServerManager().validateServer(targetServer);
        if (!serverValidation.isValid()) {
            String serverNotFound = configManager.getString("messages.server-not-found", "&c服务器 &e{server} &c不存在或未启用！");
            serverNotFound = MessageUtils.replacePlaceholder(serverNotFound, "server", targetServer);
            player.sendMessage(MessageUtils.colorize(serverNotFound));
            return;
        }

        // 检查冷却时间
        if (plugin.getCooldownManager().isOnCooldown(player)) {
            long remainingTime = plugin.getCooldownManager().getRemainingCooldown(player);
            String cooldownMessage = configManager.getString("messages.cooldown-active", "&c请等待 &e{time} &c秒后再次使用传送！");
            cooldownMessage = MessageUtils.replacePlaceholder(cooldownMessage, "time", String.valueOf(remainingTime));
            player.sendMessage(MessageUtils.colorize(cooldownMessage));
            return;
        }

        // 执行传送
        executeTeleport(player, serverValidation.getActualServerName(), player.getUsername());
    }
    
    /**
     * 处理传送他人
     */
    private void handleOtherTeleport(CommandSource source, String[] args) {
        if (args.length < 3) {
            source.sendMessage(MessageUtils.error("用法: /stp tpto <玩家> <服务器>"));
            return;
        }

        String targetPlayer = args[1];
        String targetServer = args[2];
        // 检查权限
        if (!plugin.getPermissionManager().canTeleportOthers(source)) {
            String noPermission = configManager.getString("messages.no-permission", "&c你没有权限执行此操作！");
            source.sendMessage(MessageUtils.colorize(noPermission));
            return;
        }

        // 验证服务器
        var serverValidation = plugin.getServerManager().validateServer(targetServer);
        if (!serverValidation.isValid()) {
            String serverNotFound = configManager.getString("messages.server-not-found", "&c服务器 &e{server} &c不存在或未启用！");
            serverNotFound = MessageUtils.replacePlaceholder(serverNotFound, "server", targetServer);
            source.sendMessage(MessageUtils.colorize(serverNotFound));
            return;
        }

        // 检查目标玩家是否在线
        Optional<Player> targetPlayerObj = proxy.getPlayer(targetPlayer);
        if (!targetPlayerObj.isPresent()) {
            String playerNotFound = configManager.getString("messages.player-not-found", "&c玩家 &e{player} &c不存在或不在线！");
            playerNotFound = MessageUtils.replacePlaceholder(playerNotFound, "player", targetPlayer);
            source.sendMessage(MessageUtils.colorize(playerNotFound));
            return;
        }

        String initiator = source instanceof Player ? ((Player) source).getUsername() : "控制台";

        // 执行传送
        executeTeleport(targetPlayerObj.get(), serverValidation.getActualServerName(), initiator);
    }

    /**
     * 执行传送操作
     * @param player 要传送的玩家
     * @param targetServer 目标服务器
     * @param initiator 发起者
     */
    private void executeTeleport(Player player, String targetServer, String initiator) {
        try {
            // 检查玩家当前是否已经在目标服务器
            Optional<RegisteredServer> currentServer = player.getCurrentServer()
                .map(connection -> connection.getServer());

            if (currentServer.isPresent() &&
                currentServer.get().getServerInfo().getName().equals(targetServer)) {

                String alreadyOnServer = "玩家已经在目标服务器 " + targetServer;
                if (initiator.equals(player.getUsername())) {
                    player.sendMessage(MessageUtils.warning("你已经在服务器 " + targetServer + " 上了！"));
                } else {
                    CommandSource initiatorSource = proxy.getPlayer(initiator).map(p -> (CommandSource) p)
                        .orElse(proxy.getConsoleCommandSource());
                    initiatorSource.sendMessage(MessageUtils.warning(alreadyOnServer));
                }
                return;
            }

            // 设置冷却时间（如果是自己传送）
            if (initiator.equals(player.getUsername())) {
                plugin.getCooldownManager().setCooldown(player);
            }

            // 执行传送（现在包含直接传送和可选的增强功能）
            boolean teleportSuccess = plugin.getMessageSender().sendTeleportPlayerMessage(
                player.getUsername(), targetServer, initiator);

            if (teleportSuccess) {
                // 传送成功，通知发起者
                String successMessage = configManager.getString("messages.teleport-success",
                    "&a成功传送到服务器 &e{server}&a！");
                successMessage = MessageUtils.replacePlaceholder(successMessage, "server", targetServer);

                if (initiator.equals(player.getUsername())) {
                    player.sendMessage(MessageUtils.colorize(successMessage));
                } else {
                    String othersSuccessMessage = configManager.getString("messages.teleport-others-success",
                        "&a成功将玩家 &e{player} &a传送到服务器 &e{server}&a！");
                    othersSuccessMessage = MessageUtils.replacePlaceholders(othersSuccessMessage,
                        "player", player.getUsername(), "server", targetServer);

                    CommandSource initiatorSource = proxy.getPlayer(initiator).map(p -> (CommandSource) p)
                        .orElse(proxy.getConsoleCommandSource());
                    initiatorSource.sendMessage(MessageUtils.colorize(othersSuccessMessage));
                }

                logger.info("传送成功 - 玩家: {}, 目标服务器: {}, 发起者: {}",
                    player.getUsername(), targetServer, initiator);
            } else {
                // 传送失败
                String failedMessage = "传送失败，请检查服务器状态或稍后重试";
                if (initiator.equals(player.getUsername())) {
                    player.sendMessage(MessageUtils.error(failedMessage));
                } else {
                    CommandSource initiatorSource = proxy.getPlayer(initiator).map(p -> (CommandSource) p)
                        .orElse(proxy.getConsoleCommandSource());
                    initiatorSource.sendMessage(MessageUtils.error(failedMessage));
                }

                // 移除冷却时间（如果设置了）
                if (initiator.equals(player.getUsername())) {
                    plugin.getCooldownManager().removeCooldown(player);
                }
            }

        } catch (Exception e) {
            logger.error("执行传送时发生错误", e);

            String errorMessage = "传送时发生内部错误";
            if (initiator.equals(player.getUsername())) {
                player.sendMessage(MessageUtils.error(errorMessage));
            } else {
                CommandSource initiatorSource = proxy.getPlayer(initiator).map(p -> (CommandSource) p)
                    .orElse(proxy.getConsoleCommandSource());
                initiatorSource.sendMessage(MessageUtils.error(errorMessage));
            }
        }
    }
    
    @Override
    public boolean hasPermission(Invocation invocation) {
        // 基础权限检查
        return invocation.source().hasPermission(ProtocolConstants.Permissions.BASE);
    }
    
    @Override
    public List<String> suggest(Invocation invocation) {
        return suggestAsync(invocation).join();
    }
    
    @Override
    public CompletableFuture<List<String>> suggestAsync(Invocation invocation) {
        String[] args = invocation.arguments();
        List<String> suggestions = new ArrayList<>();
        
        if (args.length == 1) {
            // 第一个参数：子指令或服务器名
            String input = args[0].toLowerCase();
            
            // 添加子指令建议
            if ("help".startsWith(input) || "帮助".startsWith(input)) {
                suggestions.add("help");
            }
            if ("list".startsWith(input) || "列表".startsWith(input)) {
                suggestions.add("list");
            }
            if (invocation.source().hasPermission(ProtocolConstants.Permissions.ADMIN) && 
                ("reload".startsWith(input) || "重载".startsWith(input))) {
                suggestions.add("reload");
            }
            
            // 添加服务器名建议
            List<String> enabledServers = configManager.getStringList("servers.enabled-servers", new ArrayList<>());
            for (String server : enabledServers) {
                if (server.toLowerCase().startsWith(input)) {
                    suggestions.add(server);
                }
            }
            
            // 如果有传送他人权限，添加玩家名建议
            if (invocation.source().hasPermission(ProtocolConstants.Permissions.TELEPORT_OTHERS)) {
                for (Player player : proxy.getAllPlayers()) {
                    if (player.getUsername().toLowerCase().startsWith(input)) {
                        suggestions.add(player.getUsername());
                    }
                }
            }
            
        } else if (args.length == 2 && 
                   invocation.source().hasPermission(ProtocolConstants.Permissions.TELEPORT_OTHERS)) {
            // 第二个参数：服务器名（当第一个参数是玩家名时）
            String input = args[1].toLowerCase();
            List<String> enabledServers = configManager.getStringList("servers.enabled-servers", new ArrayList<>());
            for (String server : enabledServers) {
                if (server.toLowerCase().startsWith(input)) {
                    suggestions.add(server);
                }
            }
        }
        
        return CompletableFuture.completedFuture(suggestions);
    }
}
