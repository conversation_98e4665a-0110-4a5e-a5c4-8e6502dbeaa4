package org.PlayerServerTPpaper.playerServerTPVelocity.config;

import com.velocitypowered.api.plugin.annotation.DataDirectory;
import org.slf4j.Logger;
import org.yaml.snakeyaml.Yaml;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;

/**
 * 配置文件管理器
 * 负责加载、保存和管理插件配置
 */
public class ConfigManager {
    
    private final Path dataDirectory;
    private final Logger logger;
    private final Yaml yaml;
    private Map<String, Object> config;
    private final String configFileName = "config.yml";
    
    public ConfigManager(@DataDirectory Path dataDirectory, Logger logger) {
        this.dataDirectory = dataDirectory;
        this.logger = logger;
        this.yaml = new Yaml();
        this.config = new HashMap<>();
    }
    
    /**
     * 加载配置文件
     * 如果配置文件不存在，则创建默认配置
     */
    public void loadConfig() {
        try {
            // 确保数据目录存在
            if (!Files.exists(dataDirectory)) {
                Files.createDirectories(dataDirectory);
            }
            
            Path configFile = dataDirectory.resolve(configFileName);
            
            // 如果配置文件不存在，从资源中复制默认配置
            if (!Files.exists(configFile)) {
                copyDefaultConfig(configFile);
            }
            
            // 加载配置文件
            try (InputStream inputStream = Files.newInputStream(configFile)) {
                config = yaml.load(inputStream);
                if (config == null) {
                    config = new HashMap<>();
                }
            }
            
            logger.info("配置文件加载成功");
            
        } catch (IOException e) {
            logger.error("加载配置文件时发生错误", e);
            config = new HashMap<>();
        }
    }
    
    /**
     * 从资源文件复制默认配置
     */
    private void copyDefaultConfig(Path configFile) throws IOException {
        try (InputStream resourceStream = getClass().getClassLoader().getResourceAsStream(configFileName)) {
            if (resourceStream != null) {
                Files.copy(resourceStream, configFile);
                logger.info("已创建默认配置文件");
            } else {
                logger.warn("无法找到默认配置文件资源");
            }
        }
    }
    
    /**
     * 保存配置文件
     */
    public void saveConfig() {
        try {
            Path configFile = dataDirectory.resolve(configFileName);
            try (FileWriter writer = new FileWriter(configFile.toFile())) {
                yaml.dump(config, writer);
            }
            logger.info("配置文件保存成功");
        } catch (IOException e) {
            logger.error("保存配置文件时发生错误", e);
        }
    }
    
    /**
     * 重新加载配置文件
     */
    public void reloadConfig() {
        loadConfig();
    }
    
    /**
     * 获取字符串配置值
     */
    public String getString(String path, String defaultValue) {
        Object value = getNestedValue(path);
        return value instanceof String ? (String) value : defaultValue;
    }
    
    /**
     * 获取整数配置值
     */
    public int getInt(String path, int defaultValue) {
        Object value = getNestedValue(path);
        return value instanceof Number ? ((Number) value).intValue() : defaultValue;
    }
    
    /**
     * 获取长整数配置值
     */
    public long getLong(String path, long defaultValue) {
        Object value = getNestedValue(path);
        return value instanceof Number ? ((Number) value).longValue() : defaultValue;
    }
    
    /**
     * 获取布尔配置值
     */
    public boolean getBoolean(String path, boolean defaultValue) {
        Object value = getNestedValue(path);
        return value instanceof Boolean ? (Boolean) value : defaultValue;
    }
    
    /**
     * 获取字符串列表配置值
     */
    @SuppressWarnings("unchecked")
    public List<String> getStringList(String path, List<String> defaultValue) {
        Object value = getNestedValue(path);
        if (value instanceof List) {
            List<?> list = (List<?>) value;
            List<String> stringList = new ArrayList<>();
            for (Object item : list) {
                if (item != null) {
                    stringList.add(item.toString());
                }
            }
            return stringList;
        }
        return defaultValue != null ? defaultValue : new ArrayList<>();
    }
    
    /**
     * 获取嵌套配置值
     */
    private Object getNestedValue(String path) {
        String[] keys = path.split("\\.");
        Object current = config;
        
        for (String key : keys) {
            if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(key);
            } else {
                return null;
            }
        }
        
        return current;
    }
    
    /**
     * 设置配置值
     */
    @SuppressWarnings("unchecked")
    public void set(String path, Object value) {
        String[] keys = path.split("\\.");
        Map<String, Object> current = config;

        for (int i = 0; i < keys.length - 1; i++) {
            String key = keys[i];
            Object next = current.get(key);
            if (!(next instanceof Map)) {
                next = new HashMap<String, Object>();
                current.put(key, next);
            }
            current = (Map<String, Object>) next;
        }

        current.put(keys[keys.length - 1], value);
    }

    /**
     * 获取配置节点
     * @param path 配置路径
     * @return 配置节点Map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getConfigSection(String path) {
        Object value = getNestedValue(path);
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return new HashMap<>();
    }

    /**
     * 检查配置路径是否存在
     * @param path 配置路径
     * @return 是否存在
     */
    public boolean contains(String path) {
        return getNestedValue(path) != null;
    }

    /**
     * 获取所有配置键
     * @return 配置键集合
     */
    public Set<String> getKeys() {
        return config.keySet();
    }
}
