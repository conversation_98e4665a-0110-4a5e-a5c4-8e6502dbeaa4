package org.PlayerServerTPpaper.playerServerTPVelocity.managers;

import com.velocitypowered.api.command.CommandSource;
import com.velocitypowered.api.proxy.Player;
import org.PlayerServerTPpaper.playerServerTPVelocity.config.ConfigManager;
import org.PlayerServerTPpaper.playerServerTPVelocity.protocol.ProtocolConstants;
import org.slf4j.Logger;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 权限管理器
 * 负责处理权限检查、默认权限和权限验证
 */
public class PermissionManager {
    
    private final ConfigManager configManager;
    private final Logger logger;
    
    // 缓存的默认权限
    private Set<String> defaultPermissions;
    private boolean enablePermissionCheck;
    
    public PermissionManager(ConfigManager configManager, Logger logger) {
        this.configManager = configManager;
        this.logger = logger;
        
        // 初始化权限缓存
        refreshPermissionCache();
    }
    
    /**
     * 刷新权限缓存
     * 从配置文件重新加载权限设置
     */
    public void refreshPermissionCache() {
        // 加载是否启用权限检查
        this.enablePermissionCheck = configManager.getBoolean("permissions.enable-permission-check", true);
        
        // 加载默认权限
        List<String> defaultPermissionsList = configManager.getStringList("permissions.default-permissions", 
            List.of(ProtocolConstants.Permissions.TELEPORT_SELF, ProtocolConstants.Permissions.LIST_SERVERS));
        this.defaultPermissions = new HashSet<>(defaultPermissionsList);
        
        logger.info("权限缓存已刷新，权限检查: {}, 默认权限数量: {}", 
            enablePermissionCheck, defaultPermissions.size());
    }
    
    /**
     * 检查命令源是否有指定权限
     * @param source 命令源
     * @param permission 权限节点
     * @return 是否有权限
     */
    public boolean hasPermission(CommandSource source, String permission) {
        // 如果禁用权限检查，则所有人都有权限
        if (!enablePermissionCheck) {
            return true;
        }
        
        // 控制台总是有所有权限
        if (!(source instanceof Player)) {
            return true;
        }
        
        Player player = (Player) source;
        
        // 检查是否是默认权限
        if (defaultPermissions.contains(permission)) {
            return true;
        }
        
        // 检查玩家是否有具体权限
        boolean hasPermission = player.hasPermission(permission);
        
        // 调试日志
        if (configManager.getBoolean("settings.debug-mode", false)) {
            logger.debug("权限检查 - 玩家: {}, 权限: {}, 结果: {}", 
                player.getUsername(), permission, hasPermission);
        }
        
        return hasPermission;
    }
    
    /**
     * 检查是否有传送自己的权限
     * @param source 命令源
     * @return 是否有权限
     */
    public boolean canTeleportSelf(CommandSource source) {
        return hasPermission(source, ProtocolConstants.Permissions.TELEPORT_SELF);
    }
    
    /**
     * 检查是否有传送他人的权限
     * @param source 命令源
     * @return 是否有权限
     */
    public boolean canTeleportOthers(CommandSource source) {
        return hasPermission(source, ProtocolConstants.Permissions.TELEPORT_OTHERS);
    }
    
    /**
     * 检查是否有查看服务器列表的权限
     * @param source 命令源
     * @return 是否有权限
     */
    public boolean canListServers(CommandSource source) {
        return hasPermission(source, ProtocolConstants.Permissions.LIST_SERVERS);
    }
    
    /**
     * 检查是否有绕过冷却时间的权限
     * @param source 命令源
     * @return 是否有权限
     */
    public boolean canBypassCooldown(CommandSource source) {
        return hasPermission(source, ProtocolConstants.Permissions.BYPASS_COOLDOWN);
    }
    
    /**
     * 检查是否有管理员权限
     * @param source 命令源
     * @return 是否有权限
     */
    public boolean isAdmin(CommandSource source) {
        return hasPermission(source, ProtocolConstants.Permissions.ADMIN);
    }
    
    /**
     * 检查是否有基础权限（使用插件的权限）
     * @param source 命令源
     * @return 是否有权限
     */
    public boolean hasBasePermission(CommandSource source) {
        return hasPermission(source, ProtocolConstants.Permissions.BASE);
    }
    
    /**
     * 检查是否有传送到特定服务器的权限
     * @param source 命令源
     * @param serverName 服务器名称
     * @return 是否有权限
     */
    public boolean canTeleportToServer(CommandSource source, String serverName) {
        // 首先检查基础传送权限
        if (!canTeleportSelf(source)) {
            return false;
        }
        
        // 检查特定服务器权限
        String serverPermission = ProtocolConstants.Permissions.BASE + ".server." + serverName.toLowerCase();
        
        // 如果有管理员权限，可以传送到任何服务器
        if (isAdmin(source)) {
            return true;
        }
        
        // 检查特定服务器权限
        return hasPermission(source, serverPermission);
    }
    
    /**
     * 获取权限检查结果详情
     * @param source 命令源
     * @param permission 权限节点
     * @return 权限检查结果
     */
    public PermissionCheckResult checkPermissionDetailed(CommandSource source, String permission) {
        if (!enablePermissionCheck) {
            return new PermissionCheckResult(true, "权限检查已禁用");
        }
        
        if (!(source instanceof Player)) {
            return new PermissionCheckResult(true, "控制台拥有所有权限");
        }
        
        Player player = (Player) source;
        
        if (defaultPermissions.contains(permission)) {
            return new PermissionCheckResult(true, "默认权限");
        }
        
        boolean hasPermission = player.hasPermission(permission);
        String reason = hasPermission ? "玩家拥有权限" : "玩家缺少权限";
        
        return new PermissionCheckResult(hasPermission, reason);
    }
    
    /**
     * 获取玩家的所有相关权限状态
     * @param player 玩家
     * @return 权限状态信息
     */
    public PlayerPermissionInfo getPlayerPermissionInfo(Player player) {
        return new PlayerPermissionInfo(
            player.getUsername(),
            canTeleportSelf(player),
            canTeleportOthers(player),
            canListServers(player),
            canBypassCooldown(player),
            isAdmin(player)
        );
    }
    
    /**
     * 是否启用权限检查
     * @return 是否启用
     */
    public boolean isPermissionCheckEnabled() {
        return enablePermissionCheck;
    }
    
    /**
     * 获取默认权限集合
     * @return 默认权限
     */
    public Set<String> getDefaultPermissions() {
        return new HashSet<>(defaultPermissions);
    }
    
    /**
     * 权限检查结果类
     */
    public static class PermissionCheckResult {
        private final boolean hasPermission;
        private final String reason;
        
        public PermissionCheckResult(boolean hasPermission, String reason) {
            this.hasPermission = hasPermission;
            this.reason = reason;
        }
        
        public boolean hasPermission() {
            return hasPermission;
        }
        
        public String getReason() {
            return reason;
        }
    }
    
    /**
     * 玩家权限信息类
     */
    public static class PlayerPermissionInfo {
        private final String playerName;
        private final boolean canTeleportSelf;
        private final boolean canTeleportOthers;
        private final boolean canListServers;
        private final boolean canBypassCooldown;
        private final boolean isAdmin;
        
        public PlayerPermissionInfo(String playerName, boolean canTeleportSelf, boolean canTeleportOthers,
                                  boolean canListServers, boolean canBypassCooldown, boolean isAdmin) {
            this.playerName = playerName;
            this.canTeleportSelf = canTeleportSelf;
            this.canTeleportOthers = canTeleportOthers;
            this.canListServers = canListServers;
            this.canBypassCooldown = canBypassCooldown;
            this.isAdmin = isAdmin;
        }
        
        // Getters
        public String getPlayerName() { return playerName; }
        public boolean canTeleportSelf() { return canTeleportSelf; }
        public boolean canTeleportOthers() { return canTeleportOthers; }
        public boolean canListServers() { return canListServers; }
        public boolean canBypassCooldown() { return canBypassCooldown; }
        public boolean isAdmin() { return isAdmin; }
    }
}
