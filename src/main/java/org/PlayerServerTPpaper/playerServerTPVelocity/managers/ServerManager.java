package org.PlayerServerTPpaper.playerServerTPVelocity.managers;

import com.velocitypowered.api.proxy.ProxyServer;
import com.velocitypowered.api.proxy.server.RegisteredServer;
import org.PlayerServerTPpaper.playerServerTPVelocity.config.ConfigManager;
import org.slf4j.Logger;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 服务器管理器
 * 负责管理可用服务器列表、服务器验证和别名处理
 */
public class ServerManager {
    
    private final ProxyServer proxy;
    private final ConfigManager configManager;
    private final Logger logger;
    
    // 缓存的服务器信息
    private Set<String> enabledServers;
    private Map<String, String> serverAliases;
    private Map<String, String> serverDisplayNames;
    
    public ServerManager(ProxyServer proxy, ConfigManager configManager, Logger logger) {
        this.proxy = proxy;
        this.configManager = configManager;
        this.logger = logger;
        
        // 初始化缓存
        refreshServerCache();
    }
    
    /**
     * 刷新服务器缓存
     * 从配置文件重新加载服务器信息
     */
    public void refreshServerCache() {
        // 加载启用的服务器列表
        List<String> enabledServersList = configManager.getStringList("servers.enabled-servers", new ArrayList<>());
        this.enabledServers = new HashSet<>(enabledServersList);
        
        // 加载服务器别名
        this.serverAliases = new HashMap<>();
        Map<String, Object> aliasesConfig = getConfigSection("servers.aliases");
        if (aliasesConfig != null) {
            for (Map.Entry<String, Object> entry : aliasesConfig.entrySet()) {
                if (entry.getValue() instanceof String) {
                    serverAliases.put(entry.getKey().toLowerCase(), (String) entry.getValue());
                }
            }
        }
        
        // 加载服务器显示名称
        this.serverDisplayNames = new HashMap<>();
        Map<String, Object> displayNamesConfig = getConfigSection("servers.display-names");
        if (displayNamesConfig != null) {
            for (Map.Entry<String, Object> entry : displayNamesConfig.entrySet()) {
                if (entry.getValue() instanceof String) {
                    serverDisplayNames.put(entry.getKey(), (String) entry.getValue());
                }
            }
        }
        
        logger.info("服务器缓存已刷新，启用服务器数量: {}, 别名数量: {}", 
            enabledServers.size(), serverAliases.size());
    }
    
    /**
     * 获取配置文件中的节点
     */
    private Map<String, Object> getConfigSection(String path) {
        return configManager.getConfigSection(path);
    }
    
    /**
     * 验证服务器是否存在且可用
     * @param serverName 服务器名称或别名
     * @return 验证结果
     */
    public ServerValidationResult validateServer(String serverName) {
        if (serverName == null || serverName.trim().isEmpty()) {
            return new ServerValidationResult(false, null, "服务器名称不能为空");
        }
        
        // 解析服务器名称（处理别名）
        String actualServerName = resolveServerName(serverName);
        
        // 检查服务器是否在启用列表中
        if (!enabledServers.contains(actualServerName)) {
            return new ServerValidationResult(false, actualServerName, "服务器未在启用列表中");
        }
        
        // 检查服务器是否在Velocity中注册
        Optional<RegisteredServer> registeredServer = proxy.getServer(actualServerName);
        if (!registeredServer.isPresent()) {
            return new ServerValidationResult(false, actualServerName, "服务器未在Velocity中注册");
        }
        
        return new ServerValidationResult(true, actualServerName, "服务器验证成功");
    }
    
    /**
     * 异步验证服务器连接状态
     * @param serverName 服务器名称
     * @return 连接状态检查结果
     */
    public CompletableFuture<Boolean> checkServerConnection(String serverName) {
        String actualServerName = resolveServerName(serverName);
        Optional<RegisteredServer> server = proxy.getServer(actualServerName);
        
        if (!server.isPresent()) {
            return CompletableFuture.completedFuture(false);
        }
        
        // 尝试ping服务器
        return server.get().ping()
            .thenApply(ping -> true)
            .exceptionally(throwable -> {
                logger.warn("无法连接到服务器 {}: {}", actualServerName, throwable.getMessage());
                return false;
            });
    }
    
    /**
     * 解析服务器名称（处理别名）
     * @param input 输入的服务器名称或别名
     * @return 实际的服务器名称
     */
    public String resolveServerName(String input) {
        if (input == null) return null;
        
        String lowerInput = input.toLowerCase();
        
        // 检查是否是别名
        if (serverAliases.containsKey(lowerInput)) {
            return serverAliases.get(lowerInput);
        }
        
        // 返回原始输入（可能就是服务器名称）
        return input;
    }
    
    /**
     * 获取服务器的显示名称
     * @param serverName 服务器名称
     * @return 显示名称，如果没有配置则返回服务器名称本身
     */
    public String getServerDisplayName(String serverName) {
        return serverDisplayNames.getOrDefault(serverName, serverName);
    }
    
    /**
     * 获取所有启用的服务器列表
     * @return 启用的服务器集合
     */
    public Set<String> getEnabledServers() {
        return new HashSet<>(enabledServers);
    }
    
    /**
     * 获取所有服务器别名
     * @return 别名映射
     */
    public Map<String, String> getServerAliases() {
        return new HashMap<>(serverAliases);
    }
    
    /**
     * 检查服务器是否启用
     * @param serverName 服务器名称
     * @return 是否启用
     */
    public boolean isServerEnabled(String serverName) {
        String actualServerName = resolveServerName(serverName);
        return enabledServers.contains(actualServerName);
    }
    
    /**
     * 获取Velocity中注册的服务器
     * @param serverName 服务器名称
     * @return 注册的服务器对象
     */
    public Optional<RegisteredServer> getRegisteredServer(String serverName) {
        String actualServerName = resolveServerName(serverName);
        return proxy.getServer(actualServerName);
    }
    
    /**
     * 获取服务器统计信息
     * @return 服务器统计信息
     */
    public ServerStats getServerStats() {
        int totalRegistered = proxy.getAllServers().size();
        int totalEnabled = enabledServers.size();
        int totalAliases = serverAliases.size();
        
        return new ServerStats(totalRegistered, totalEnabled, totalAliases);
    }
    
    /**
     * 服务器验证结果类
     */
    public static class ServerValidationResult {
        private final boolean valid;
        private final String actualServerName;
        private final String message;
        
        public ServerValidationResult(boolean valid, String actualServerName, String message) {
            this.valid = valid;
            this.actualServerName = actualServerName;
            this.message = message;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getActualServerName() {
            return actualServerName;
        }
        
        public String getMessage() {
            return message;
        }
    }
    
    /**
     * 服务器统计信息类
     */
    public static class ServerStats {
        private final int totalRegistered;
        private final int totalEnabled;
        private final int totalAliases;
        
        public ServerStats(int totalRegistered, int totalEnabled, int totalAliases) {
            this.totalRegistered = totalRegistered;
            this.totalEnabled = totalEnabled;
            this.totalAliases = totalAliases;
        }
        
        public int getTotalRegistered() {
            return totalRegistered;
        }
        
        public int getTotalEnabled() {
            return totalEnabled;
        }
        
        public int getTotalAliases() {
            return totalAliases;
        }
    }
}
