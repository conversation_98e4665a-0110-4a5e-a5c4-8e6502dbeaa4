package org.PlayerServerTPpaper.playerServerTPVelocity.managers;

import com.velocitypowered.api.proxy.Player;
import com.velocitypowered.api.scheduler.ScheduledTask;
import org.PlayerServerTPpaper.playerServerTPVelocity.PlayerServerTPVelocity;
import org.PlayerServerTPpaper.playerServerTPVelocity.config.ConfigManager;
import org.slf4j.Logger;

import java.time.Duration;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 冷却时间管理器
 * 负责管理玩家传送的冷却时间
 */
public class CooldownManager {
    
    private final PlayerServerTPVelocity plugin;
    private final ConfigManager configManager;
    private final PermissionManager permissionManager;
    private final Logger logger;
    
    // 存储玩家冷却时间的Map <玩家UUID, 冷却结束时间戳>
    private final Map<UUID, Long> cooldowns;
    
    // 清理任务
    private ScheduledTask cleanupTask;
    
    public CooldownManager(PlayerServerTPVelocity plugin, ConfigManager configManager, 
                          PermissionManager permissionManager, Logger logger) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.permissionManager = permissionManager;
        this.logger = logger;
        this.cooldowns = new ConcurrentHashMap<>();
        
        // 启动定期清理任务
        startCleanupTask();
    }
    
    /**
     * 启动定期清理过期冷却时间的任务
     */
    private void startCleanupTask() {
        // 每分钟清理一次过期的冷却时间
        cleanupTask = plugin.getProxy().getScheduler()
            .buildTask(plugin, this::cleanupExpiredCooldowns)
            .repeat(Duration.ofMinutes(1))
            .schedule();
        
        logger.debug("冷却时间清理任务已启动");
    }
    
    /**
     * 停止清理任务
     */
    public void shutdown() {
        if (cleanupTask != null) {
            cleanupTask.cancel();
            logger.debug("冷却时间清理任务已停止");
        }
        cooldowns.clear();
    }
    
    /**
     * 检查玩家是否在冷却时间内
     * @param player 玩家
     * @return 是否在冷却时间内
     */
    public boolean isOnCooldown(Player player) {
        // 检查是否有绕过冷却时间的权限
        if (permissionManager.canBypassCooldown(player)) {
            return false;
        }
        
        UUID playerId = player.getUniqueId();
        Long cooldownEnd = cooldowns.get(playerId);
        
        if (cooldownEnd == null) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        boolean onCooldown = currentTime < cooldownEnd;
        
        // 如果冷却时间已过，移除记录
        if (!onCooldown) {
            cooldowns.remove(playerId);
        }
        
        return onCooldown;
    }
    
    /**
     * 获取玩家剩余冷却时间（秒）
     * @param player 玩家
     * @return 剩余冷却时间，如果没有冷却时间则返回0
     */
    public long getRemainingCooldown(Player player) {
        // 检查是否有绕过冷却时间的权限
        if (permissionManager.canBypassCooldown(player)) {
            return 0;
        }
        
        UUID playerId = player.getUniqueId();
        Long cooldownEnd = cooldowns.get(playerId);
        
        if (cooldownEnd == null) {
            return 0;
        }
        
        long currentTime = System.currentTimeMillis();
        long remaining = (cooldownEnd - currentTime) / 1000;
        
        return Math.max(0, remaining);
    }
    
    /**
     * 为玩家设置冷却时间
     * @param player 玩家
     */
    public void setCooldown(Player player) {
        // 检查是否有绕过冷却时间的权限
        if (permissionManager.canBypassCooldown(player)) {
            return;
        }
        
        long cooldownTime = configManager.getLong("settings.cooldown-time", 5);
        
        // 如果冷却时间为0或负数，则不设置冷却时间
        if (cooldownTime <= 0) {
            return;
        }
        
        UUID playerId = player.getUniqueId();
        long cooldownEnd = System.currentTimeMillis() + (cooldownTime * 1000);
        
        cooldowns.put(playerId, cooldownEnd);
        
        if (configManager.getBoolean("settings.debug-mode", false)) {
            logger.debug("为玩家 {} 设置了 {} 秒的冷却时间", player.getUsername(), cooldownTime);
        }
    }
    
    /**
     * 为玩家设置自定义冷却时间
     * @param player 玩家
     * @param seconds 冷却时间（秒）
     */
    public void setCooldown(Player player, long seconds) {
        // 检查是否有绕过冷却时间的权限
        if (permissionManager.canBypassCooldown(player)) {
            return;
        }
        
        if (seconds <= 0) {
            return;
        }
        
        UUID playerId = player.getUniqueId();
        long cooldownEnd = System.currentTimeMillis() + (seconds * 1000);
        
        cooldowns.put(playerId, cooldownEnd);
        
        if (configManager.getBoolean("settings.debug-mode", false)) {
            logger.debug("为玩家 {} 设置了 {} 秒的自定义冷却时间", player.getUsername(), seconds);
        }
    }
    
    /**
     * 移除玩家的冷却时间
     * @param player 玩家
     */
    public void removeCooldown(Player player) {
        UUID playerId = player.getUniqueId();
        boolean removed = cooldowns.remove(playerId) != null;
        
        if (removed && configManager.getBoolean("settings.debug-mode", false)) {
            logger.debug("移除了玩家 {} 的冷却时间", player.getUsername());
        }
    }
    
    /**
     * 清理所有过期的冷却时间
     */
    private void cleanupExpiredCooldowns() {
        long currentTime = System.currentTimeMillis();
        int removedCount = 0;
        
        // 使用迭代器安全地移除过期项
        cooldowns.entrySet().removeIf(entry -> {
            boolean expired = currentTime >= entry.getValue();
            return expired;
        });
        
        if (removedCount > 0 && configManager.getBoolean("settings.debug-mode", false)) {
            logger.debug("清理了 {} 个过期的冷却时间记录", removedCount);
        }
    }
    
    /**
     * 获取当前冷却时间统计信息
     * @return 冷却时间统计
     */
    public CooldownStats getCooldownStats() {
        int activeCooldowns = cooldowns.size();
        long configuredCooldownTime = configManager.getLong("settings.cooldown-time", 5);
        
        return new CooldownStats(activeCooldowns, configuredCooldownTime);
    }
    
    /**
     * 检查冷却时间配置是否有效
     * @return 冷却时间检查结果
     */
    public CooldownCheckResult checkCooldownConfig() {
        long cooldownTime = configManager.getLong("settings.cooldown-time", 5);
        
        if (cooldownTime < 0) {
            return new CooldownCheckResult(false, "冷却时间不能为负数");
        }
        
        if (cooldownTime == 0) {
            return new CooldownCheckResult(true, "冷却时间已禁用");
        }
        
        if (cooldownTime > 3600) {
            return new CooldownCheckResult(false, "冷却时间过长（超过1小时）");
        }
        
        return new CooldownCheckResult(true, "冷却时间配置正常");
    }
    
    /**
     * 获取所有在冷却时间内的玩家数量
     * @return 在冷却时间内的玩家数量
     */
    public int getActiveCooldownCount() {
        // 清理过期的冷却时间
        cleanupExpiredCooldowns();
        return cooldowns.size();
    }
    
    /**
     * 冷却时间统计信息类
     */
    public static class CooldownStats {
        private final int activeCooldowns;
        private final long configuredCooldownTime;
        
        public CooldownStats(int activeCooldowns, long configuredCooldownTime) {
            this.activeCooldowns = activeCooldowns;
            this.configuredCooldownTime = configuredCooldownTime;
        }
        
        public int getActiveCooldowns() {
            return activeCooldowns;
        }
        
        public long getConfiguredCooldownTime() {
            return configuredCooldownTime;
        }
    }
    
    /**
     * 冷却时间检查结果类
     */
    public static class CooldownCheckResult {
        private final boolean valid;
        private final String message;
        
        public CooldownCheckResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getMessage() {
            return message;
        }
    }
}
