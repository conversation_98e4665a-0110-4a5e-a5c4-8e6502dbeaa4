package org.PlayerServerTPpaper.playerServerTPVelocity.utils;

import com.velocitypowered.api.command.CommandSource;
import com.velocitypowered.api.proxy.Player;
import org.PlayerServerTPpaper.playerServerTPVelocity.PlayerServerTPVelocity;
import org.PlayerServerTPpaper.playerServerTPVelocity.config.ConfigManager;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * 调试管理器
 * 提供调试信息和诊断工具
 */
public class DebugManager {
    
    private final PlayerServerTPVelocity plugin;
    private final ConfigManager configManager;
    private final Logger logger;
    
    public DebugManager(PlayerServerTPVelocity plugin, ConfigManager configManager, Logger logger) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.logger = logger;
    }
    
    /**
     * 获取系统状态信息
     * @param source 命令源
     * @return 状态信息列表
     */
    public List<String> getSystemStatus(CommandSource source) {
        List<String> statusLines = new ArrayList<>();
        
        statusLines.add("&6=== PlayerServerTP 系统状态 ===");
        
        // 基础信息
        statusLines.add("&e基础信息:");
        statusLines.add("&f  插件版本: &a1.0-SNAPSHOT");
        statusLines.add("&f  调试模式: " + (configManager.getBoolean("settings.debug-mode", false) ? "&a启用" : "&c禁用"));
        statusLines.add("&f  在线玩家: &e" + plugin.getProxy().getPlayerCount());
        statusLines.add("&f  注册服务器: &e" + plugin.getProxy().getAllServers().size());
        
        // 管理器状态
        statusLines.add("&e管理器状态:");
        statusLines.add("&f  服务器管理器: " + getManagerStatus(plugin.getServerManager()));
        statusLines.add("&f  权限管理器: " + getManagerStatus(plugin.getPermissionManager()));
        statusLines.add("&f  冷却管理器: " + getManagerStatus(plugin.getCooldownManager()));
        statusLines.add("&f  消息发送器: " + getManagerStatus(plugin.getMessageSender()));
        
        // 配置状态
        statusLines.add("&e配置状态:");
        statusLines.add("&f  冷却时间: &e" + configManager.getLong("settings.cooldown-time", 5) + "秒");
        statusLines.add("&f  启用服务器数: &e" + configManager.getStringList("servers.enabled-servers", new ArrayList<>()).size());
        statusLines.add("&f  权限检查: " + (configManager.getBoolean("permissions.enable-permission-check", true) ? "&a启用" : "&c禁用"));
        
        return statusLines;
    }
    
    /**
     * 获取性能信息
     * @return 性能信息列表
     */
    public List<String> getPerformanceInfo() {
        List<String> perfLines = new ArrayList<>();
        
        perfLines.add("&6=== 性能信息 ===");
        
        // JVM信息
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory() / 1024 / 1024;
        long totalMemory = runtime.totalMemory() / 1024 / 1024;
        long freeMemory = runtime.freeMemory() / 1024 / 1024;
        long usedMemory = totalMemory - freeMemory;
        
        perfLines.add("&e内存使用:");
        perfLines.add("&f  已使用: &e" + usedMemory + "MB");
        perfLines.add("&f  总分配: &e" + totalMemory + "MB");
        perfLines.add("&f  最大可用: &e" + maxMemory + "MB");
        perfLines.add("&f  使用率: &e" + String.format("%.1f%%", (double) usedMemory / maxMemory * 100));
        
        // 系统信息
        perfLines.add("&e系统信息:");
        perfLines.add("&f  Java版本: &e" + System.getProperty("java.version"));
        perfLines.add("&f  操作系统: &e" + System.getProperty("os.name"));
        perfLines.add("&f  CPU核心数: &e" + Runtime.getRuntime().availableProcessors());
        
        return perfLines;
    }
    
    /**
     * 获取网络状态
     * @return 网络状态信息
     */
    public List<String> getNetworkStatus() {
        List<String> networkLines = new ArrayList<>();
        
        networkLines.add("&6=== 网络状态 ===");
        
        // 服务器连接状态
        networkLines.add("&e服务器连接:");
        var serverManager = plugin.getServerManager();
        if (serverManager != null) {
            var stats = serverManager.getServerStats();
            networkLines.add("&f  注册服务器: &e" + stats.getTotalRegistered());
            networkLines.add("&f  启用服务器: &e" + stats.getTotalEnabled());
            networkLines.add("&f  服务器别名: &e" + stats.getTotalAliases());
        }
        
        // 消息发送状态
        networkLines.add("&e消息系统:");
        var messageSender = plugin.getMessageSender();
        if (messageSender != null) {
            var messageStats = messageSender.getMessageStats();
            networkLines.add("&f  在线玩家: &e" + messageStats.getOnlinePlayers());
            networkLines.add("&f  可发送消息: " + (messageStats.canSendMessages() ? "&a是" : "&c否"));
        }
        
        return networkLines;
    }
    
    /**
     * 执行系统诊断
     * @return 诊断结果
     */
    public List<String> runDiagnostics() {
        List<String> diagnosticLines = new ArrayList<>();
        
        diagnosticLines.add("&6=== 系统诊断 ===");
        
        // 检查配置文件
        diagnosticLines.add("&e配置检查:");
        if (configManager.contains("settings")) {
            diagnosticLines.add("&f  配置文件: &a正常");
        } else {
            diagnosticLines.add("&f  配置文件: &c异常 - 缺少settings节点");
        }
        
        // 检查服务器列表
        List<String> enabledServers = configManager.getStringList("servers.enabled-servers", new ArrayList<>());
        if (!enabledServers.isEmpty()) {
            diagnosticLines.add("&f  服务器列表: &a正常 (" + enabledServers.size() + "个)");
        } else {
            diagnosticLines.add("&f  服务器列表: &c警告 - 没有启用的服务器");
        }
        
        // 检查权限配置
        boolean permissionCheck = configManager.getBoolean("permissions.enable-permission-check", true);
        diagnosticLines.add("&f  权限系统: " + (permissionCheck ? "&a启用" : "&e禁用"));
        
        // 检查冷却时间配置
        long cooldownTime = configManager.getLong("settings.cooldown-time", 5);
        if (cooldownTime >= 0 && cooldownTime <= 3600) {
            diagnosticLines.add("&f  冷却时间: &a正常 (" + cooldownTime + "秒)");
        } else {
            diagnosticLines.add("&f  冷却时间: &c异常 - 值超出合理范围");
        }
        
        // 检查组件状态
        diagnosticLines.add("&e组件检查:");
        diagnosticLines.add("&f  服务器管理器: " + getComponentHealth(plugin.getServerManager()));
        diagnosticLines.add("&f  权限管理器: " + getComponentHealth(plugin.getPermissionManager()));
        diagnosticLines.add("&f  冷却管理器: " + getComponentHealth(plugin.getCooldownManager()));
        diagnosticLines.add("&f  消息发送器: " + getComponentHealth(plugin.getMessageSender()));
        
        return diagnosticLines;
    }
    
    /**
     * 获取管理器状态
     * @param manager 管理器对象
     * @return 状态字符串
     */
    private String getManagerStatus(Object manager) {
        return manager != null ? "&a正常" : "&c未初始化";
    }
    
    /**
     * 获取组件健康状态
     * @param component 组件对象
     * @return 健康状态字符串
     */
    private String getComponentHealth(Object component) {
        if (component == null) {
            return "&c未初始化";
        }
        
        try {
            // 这里可以添加更详细的健康检查逻辑
            return "&a健康";
        } catch (Exception e) {
            return "&c异常: " + e.getMessage();
        }
    }
    
    /**
     * 记录调试信息
     * @param message 调试消息
     * @param source 命令源
     */
    public void logDebugInfo(String message, CommandSource source) {
        if (configManager.getBoolean("settings.debug-mode", false)) {
            String playerName = source instanceof Player ? ((Player) source).getUsername() : "控制台";
            logger.info("[调试] {}: {}", playerName, message);
        }
    }
    
    /**
     * 切换调试模式
     * @return 新的调试模式状态
     */
    public boolean toggleDebugMode() {
        boolean currentMode = configManager.getBoolean("settings.debug-mode", false);
        boolean newMode = !currentMode;
        
        configManager.set("settings.debug-mode", newMode);
        configManager.saveConfig();
        
        logger.info("调试模式已" + (newMode ? "启用" : "禁用"));
        return newMode;
    }
    
    /**
     * 获取调试统计信息
     * @return 统计信息
     */
    public String getDebugStats() {
        boolean debugMode = configManager.getBoolean("settings.debug-mode", false);
        int onlinePlayers = plugin.getProxy().getPlayerCount();
        int registeredServers = plugin.getProxy().getAllServers().size();
        
        return String.format("调试模式: %s, 在线玩家: %d, 注册服务器: %d", 
            debugMode ? "启用" : "禁用", onlinePlayers, registeredServers);
    }
}
