package org.PlayerServerTPpaper.playerServerTPVelocity.utils;

import com.velocitypowered.api.command.CommandSource;
import com.velocitypowered.api.proxy.Player;
import org.PlayerServerTPpaper.playerServerTPVelocity.config.ConfigManager;
import org.PlayerServerTPpaper.playerServerTPVelocity.protocol.ProtocolConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * 帮助系统管理器
 * 提供插件使用帮助和权限节点文档
 */
public class HelpManager {
    
    private final ConfigManager configManager;
    
    public HelpManager(ConfigManager configManager) {
        this.configManager = configManager;
    }
    
    /**
     * 获取完整的帮助信息
     * @param source 命令源
     * @return 帮助信息列表
     */
    public List<String> getFullHelp(CommandSource source) {
        List<String> helpLines = new ArrayList<>();
        
        // 标题
        String header = configManager.getString("messages.help-header", "&6=== PlayerServerTP 帮助 ===");
        helpLines.add(header);
        
        // 基础指令
        helpLines.add("&e基础指令:");
        helpLines.add("&f  /stp help &7- 显示帮助信息");
        helpLines.add("&f  /stp list &7- 查看可用服务器列表");
        
        // 传送指令
        if (source.hasPermission(ProtocolConstants.Permissions.TELEPORT_SELF)) {
            helpLines.add("&e传送指令:");
            helpLines.add("&f  /stp <服务器> &7- 传送到指定服务器");
        }
        
        // 管理指令
        if (source.hasPermission(ProtocolConstants.Permissions.TELEPORT_OTHERS)) {
            helpLines.add("&e管理指令:");
            helpLines.add("&f  /stp <玩家> <服务器> &7- 传送玩家到指定服务器");
        }
        
        // 管理员指令
        if (source.hasPermission(ProtocolConstants.Permissions.ADMIN)) {
            helpLines.add("&e管理员指令:");
            helpLines.add("&f  /stp reload &7- 重载配置文件");
        }
        
        // 权限信息
        helpLines.add("&e权限节点:");
        helpLines.addAll(getPermissionHelp(source));
        
        // 使用示例
        helpLines.add("&e使用示例:");
        helpLines.add("&f  /stp lobby &7- 传送到大厅服务器");
        helpLines.add("&f  /stp list &7- 查看所有可用服务器");
        
        if (source.hasPermission(ProtocolConstants.Permissions.TELEPORT_OTHERS)) {
            helpLines.add("&f  /stp Steve survival &7- 传送Steve到生存服务器");
        }
        
        return helpLines;
    }
    
    /**
     * 获取权限帮助信息
     * @param source 命令源
     * @return 权限帮助列表
     */
    public List<String> getPermissionHelp(CommandSource source) {
        List<String> permissionLines = new ArrayList<>();
        
        // 基础权限
        permissionLines.add("&f  " + ProtocolConstants.Permissions.BASE + " &7- 使用插件的基础权限");
        
        // 传送权限
        if (source.hasPermission(ProtocolConstants.Permissions.TELEPORT_SELF)) {
            permissionLines.add("&f  " + ProtocolConstants.Permissions.TELEPORT_SELF + " &7- 传送自己");
        }
        
        if (source.hasPermission(ProtocolConstants.Permissions.TELEPORT_OTHERS)) {
            permissionLines.add("&f  " + ProtocolConstants.Permissions.TELEPORT_OTHERS + " &7- 传送他人");
        }
        
        // 其他权限
        if (source.hasPermission(ProtocolConstants.Permissions.LIST_SERVERS)) {
            permissionLines.add("&f  " + ProtocolConstants.Permissions.LIST_SERVERS + " &7- 查看服务器列表");
        }
        
        if (source.hasPermission(ProtocolConstants.Permissions.BYPASS_COOLDOWN)) {
            permissionLines.add("&f  " + ProtocolConstants.Permissions.BYPASS_COOLDOWN + " &7- 绕过冷却时间");
        }
        
        if (source.hasPermission(ProtocolConstants.Permissions.ADMIN)) {
            permissionLines.add("&f  " + ProtocolConstants.Permissions.ADMIN + " &7- 管理员权限");
        }
        
        return permissionLines;
    }
    
    /**
     * 获取故障排除指南
     * @return 故障排除信息
     */
    public List<String> getTroubleshootingGuide() {
        List<String> troubleshootingLines = new ArrayList<>();
        
        troubleshootingLines.add("&6=== 故障排除指南 ===");
        troubleshootingLines.add("&e常见问题:");
        troubleshootingLines.add("&f1. 传送失败");
        troubleshootingLines.add("&7   - 检查目标服务器是否在线");
        troubleshootingLines.add("&7   - 确认服务器名称正确");
        troubleshootingLines.add("&7   - 检查是否有传送权限");
        
        troubleshootingLines.add("&f2. 冷却时间问题");
        troubleshootingLines.add("&7   - 等待冷却时间结束");
        troubleshootingLines.add("&7   - 管理员可使用绕过权限");
        
        troubleshootingLines.add("&f3. 权限问题");
        troubleshootingLines.add("&7   - 联系管理员分配权限");
        troubleshootingLines.add("&7   - 使用 /stp help 查看权限节点");
        
        troubleshootingLines.add("&f4. 服务器连接问题");
        troubleshootingLines.add("&7   - 检查网络连接");
        troubleshootingLines.add("&7   - 联系服务器管理员");
        
        return troubleshootingLines;
    }
    
    /**
     * 获取配置帮助
     * @return 配置帮助信息
     */
    public List<String> getConfigurationHelp() {
        List<String> configLines = new ArrayList<>();
        
        configLines.add("&6=== 配置文件说明 ===");
        configLines.add("&e主要配置项:");
        configLines.add("&f  settings.cooldown-time &7- 传送冷却时间(秒)");
        configLines.add("&f  settings.debug-mode &7- 调试模式开关");
        configLines.add("&f  servers.enabled-servers &7- 启用的服务器列表");
        configLines.add("&f  servers.aliases &7- 服务器别名配置");
        configLines.add("&f  permissions.enable-permission-check &7- 权限检查开关");
        
        configLines.add("&e消息配置:");
        configLines.add("&f  messages.* &7- 各种消息文本配置");
        configLines.add("&f  支持颜色代码 (&a, &c, &e 等)");
        configLines.add("&f  支持占位符 ({player}, {server}, {time} 等)");
        
        return configLines;
    }
    
    /**
     * 获取版本信息
     * @return 版本信息
     */
    public List<String> getVersionInfo() {
        List<String> versionLines = new ArrayList<>();
        
        versionLines.add("&6=== PlayerServerTP 版本信息 ===");
        versionLines.add("&f插件版本: &e1.0-SNAPSHOT");
        versionLines.add("&f支持平台: &eVelocity + Paper");
        versionLines.add("&f开发者: &ePlayerServerTP Team");
        versionLines.add("&f项目地址: &ehttps://github.com/PlayerServerTP");
        
        return versionLines;
    }
    
    /**
     * 获取快速开始指南
     * @return 快速开始指南
     */
    public List<String> getQuickStartGuide() {
        List<String> quickStartLines = new ArrayList<>();
        
        quickStartLines.add("&6=== 快速开始指南 ===");
        quickStartLines.add("&e第一次使用:");
        quickStartLines.add("&f1. 使用 &e/stp list &f查看可用服务器");
        quickStartLines.add("&f2. 使用 &e/stp <服务器名> &f传送到指定服务器");
        quickStartLines.add("&f3. 如果有管理权限，可以使用 &e/stp <玩家> <服务器> &f传送他人");
        
        quickStartLines.add("&e管理员设置:");
        quickStartLines.add("&f1. 编辑 config.yml 配置服务器列表");
        quickStartLines.add("&f2. 设置权限节点给玩家");
        quickStartLines.add("&f3. 使用 &e/stp reload &f重载配置");
        
        return quickStartLines;
    }
}
