package org.PlayerServerTPpaper.playerServerTPVelocity.protocol;

/**
 * Plugin Messaging协议常量
 * 定义通信中使用的各种常量和配置
 */
public final class ProtocolConstants {
    
    /**
     * Plugin Messaging通道名称
     * 使用符合Velocity规范的通道名称
     */
    public static final String CHANNEL_NAME = "playerservertp:main";
    
    /**
     * 自定义子通道名称
     * 用于区分我们的消息和其他插件的消息
     */
    public static final String SUB_CHANNEL = "PlayerServerTP";
    
    /**
     * 消息分隔符
     * 用于分割消息中的不同部分
     */
    public static final String MESSAGE_SEPARATOR = "|";
    
    /**
     * 服务器列表分隔符
     * 用于分割服务器列表中的服务器名称
     */
    public static final String SERVER_LIST_SEPARATOR = ",";
    
    /**
     * 传送状态常量
     */
    public static final class TeleportStatus {
        public static final String SUCCESS = "SUCCESS";
        public static final String FAILED = "FAILED";
        public static final String PLAYER_NOT_FOUND = "PLAYER_NOT_FOUND";
        public static final String SERVER_NOT_FOUND = "SERVER_NOT_FOUND";
        public static final String NO_PERMISSION = "NO_PERMISSION";
        public static final String COOLDOWN_ACTIVE = "COOLDOWN_ACTIVE";
        public static final String UNSAFE_LOCATION = "UNSAFE_LOCATION";
    }
    
    /**
     * 玩家在线状态常量
     */
    public static final class PlayerStatus {
        public static final String ONLINE = "ONLINE";
        public static final String OFFLINE = "OFFLINE";
        public static final String UNKNOWN = "UNKNOWN";
    }
    
    /**
     * 权限节点常量
     */
    public static final class Permissions {
        public static final String BASE = "playerservertp";
        public static final String TELEPORT_SELF = BASE + ".teleport.self";
        public static final String TELEPORT_OTHERS = BASE + ".teleport.others";
        public static final String LIST_SERVERS = BASE + ".list";
        public static final String BYPASS_COOLDOWN = BASE + ".bypass.cooldown";
        public static final String ADMIN = BASE + ".admin";
    }
    
    // 防止实例化
    private ProtocolConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
