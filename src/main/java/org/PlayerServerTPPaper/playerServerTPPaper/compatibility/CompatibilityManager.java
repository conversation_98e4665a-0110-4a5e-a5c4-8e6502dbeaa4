package org.PlayerServerTPPaper.playerServerTPPaper.compatibility;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerTeleportEvent;
import org.bukkit.plugin.Plugin;
import org.PlayerServerTPPaper.playerServerTPPaper.PlayerServerTPPaper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 兼容性管理器
 * 负责与其他插件的兼容性处理和集成
 */
public class CompatibilityManager {
    
    private final PlayerServerTPPaper plugin;
    private final Logger logger;
    
    // 支持的插件钩子
    private final Map<String, PluginHook> pluginHooks;
    
    // 兼容性设置
    private boolean enablePluginHooks;
    private boolean checkTeleportEvents;
    private boolean checkRegionProtection;
    
    public CompatibilityManager(PlayerServerTPPaper plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.pluginHooks = new HashMap<>();
        
        // 加载兼容性设置
        loadCompatibilitySettings();
        
        // 初始化插件钩子
        initializePluginHooks();
    }
    
    /**
     * 加载兼容性设置
     */
    private void loadCompatibilitySettings() {
        enablePluginHooks = plugin.getConfig().getBoolean("compatibility.enable-plugin-hooks", true);
        checkTeleportEvents = plugin.getConfig().getBoolean("compatibility.check-events.player-teleport-event", true);
        checkRegionProtection = plugin.getConfig().getBoolean("compatibility.check-events.region-protection", true);
        
        logger.info("兼容性设置已加载 - 插件钩子: " + enablePluginHooks + 
                   ", 事件检查: " + checkTeleportEvents + 
                   ", 区域保护: " + checkRegionProtection);
    }
    
    /**
     * 初始化插件钩子
     */
    private void initializePluginHooks() {
        if (!enablePluginHooks) {
            logger.info("插件钩子已禁用");
            return;
        }
        
        List<String> supportedPlugins = plugin.getConfig().getStringList("compatibility.supported-plugins");
        if (supportedPlugins.isEmpty()) {
            supportedPlugins = List.of("WorldGuard", "GriefPrevention", "Towny", "Factions", "LuckPerms");
        }
        
        for (String pluginName : supportedPlugins) {
            try {
                initializePluginHook(pluginName);
            } catch (Exception e) {
                logger.warning("初始化 " + pluginName + " 钩子时发生错误: " + e.getMessage());
            }
        }
        
        logger.info("已初始化 " + pluginHooks.size() + " 个插件钩子");
    }
    
    /**
     * 初始化特定插件的钩子
     * @param pluginName 插件名称
     */
    private void initializePluginHook(String pluginName) {
        Plugin targetPlugin = plugin.getServer().getPluginManager().getPlugin(pluginName);
        if (targetPlugin == null || !targetPlugin.isEnabled()) {
            logger.info("插件 " + pluginName + " 未找到或未启用");
            return;
        }
        
        PluginHook hook = createPluginHook(pluginName, targetPlugin);
        if (hook != null) {
            pluginHooks.put(pluginName, hook);
            logger.info("已加载 " + pluginName + " 兼容性钩子");
        }
    }
    
    /**
     * 创建插件钩子
     * @param pluginName 插件名称
     * @param targetPlugin 目标插件
     * @return 插件钩子实例
     */
    private PluginHook createPluginHook(String pluginName, Plugin targetPlugin) {
        switch (pluginName.toLowerCase()) {
            case "worldguard":
                return new WorldGuardHook(targetPlugin, logger);
            case "griefprevention":
                return new GriefPreventionHook(targetPlugin, logger);
            case "towny":
                return new TownyHook(targetPlugin, logger);
            case "factions":
                return new FactionsHook(targetPlugin, logger);
            case "luckperms":
                return new LuckPermsHook(targetPlugin, logger);
            default:
                logger.info("暂不支持 " + pluginName + " 的专用钩子，使用通用钩子");
                return new GenericPluginHook(targetPlugin, logger);
        }
    }
    
    /**
     * 检查玩家是否可以传送到指定位置
     * @param player 玩家
     * @param targetLocation 目标位置
     * @return 是否可以传送
     */
    public boolean canTeleport(Player player, Location targetLocation) {
        if (!enablePluginHooks) {
            return true;
        }
        
        try {
            // 检查传送事件
            if (checkTeleportEvents && !checkTeleportEvent(player, targetLocation)) {
                return false;
            }
            
            // 检查区域保护
            if (checkRegionProtection && !checkRegionPermissions(player, targetLocation)) {
                return false;
            }
            
            // 检查各个插件钩子
            for (Map.Entry<String, PluginHook> entry : pluginHooks.entrySet()) {
                try {
                    if (!entry.getValue().canTeleport(player, targetLocation)) {
                        logger.info("插件 " + entry.getKey() + " 阻止了传送");
                        return false;
                    }
                } catch (Exception e) {
                    logger.warning("检查 " + entry.getKey() + " 兼容性时发生错误: " + e.getMessage());
                    // 继续检查其他插件，不因为一个插件的错误而阻止传送
                }
            }
            
            return true;
            
        } catch (Exception e) {
            logger.severe("检查传送兼容性时发生错误: " + e.getMessage());
            e.printStackTrace();
            // 发生错误时默认允许传送，避免功能完全失效
            return true;
        }
    }
    
    /**
     * 检查传送事件是否被其他插件阻止
     * @param player 玩家
     * @param targetLocation 目标位置
     * @return 是否允许传送
     */
    private boolean checkTeleportEvent(Player player, Location targetLocation) {
        try {
            // 创建一个模拟的传送事件来检查是否会被其他插件取消
            PlayerTeleportEvent testEvent = new PlayerTeleportEvent(player, player.getLocation(), 
                targetLocation, PlayerTeleportEvent.TeleportCause.PLUGIN);
            
            // 调用事件但不实际执行传送
            plugin.getServer().getPluginManager().callEvent(testEvent);
            
            boolean allowed = !testEvent.isCancelled();
            
            if (!allowed && plugin.getConfig().getBoolean("settings.debug-mode", false)) {
                logger.info("传送事件被其他插件取消");
            }
            
            return allowed;
            
        } catch (Exception e) {
            logger.warning("检查传送事件时发生错误: " + e.getMessage());
            return true; // 发生错误时默认允许
        }
    }
    
    /**
     * 检查区域保护权限
     * @param player 玩家
     * @param targetLocation 目标位置
     * @return 是否有权限
     */
    private boolean checkRegionPermissions(Player player, Location targetLocation) {
        // 这里可以添加通用的区域保护检查逻辑
        // 具体的区域保护检查由各个插件钩子处理
        return true;
    }
    
    /**
     * 传送成功后的回调
     * @param player 玩家
     * @param fromLocation 起始位置
     * @param toLocation 目标位置
     */
    public void onTeleportSuccess(Player player, Location fromLocation, Location toLocation) {
        if (!enablePluginHooks) {
            return;
        }
        
        for (Map.Entry<String, PluginHook> entry : pluginHooks.entrySet()) {
            try {
                entry.getValue().onTeleportSuccess(player, fromLocation, toLocation);
            } catch (Exception e) {
                logger.warning("通知 " + entry.getKey() + " 传送成功时发生错误: " + e.getMessage());
            }
        }
    }
    
    /**
     * 传送失败后的回调
     * @param player 玩家
     * @param fromLocation 起始位置
     * @param toLocation 目标位置
     */
    public void onTeleportFailed(Player player, Location fromLocation, Location toLocation) {
        if (!enablePluginHooks) {
            return;
        }
        
        for (Map.Entry<String, PluginHook> entry : pluginHooks.entrySet()) {
            try {
                entry.getValue().onTeleportFailed(player, fromLocation, toLocation);
            } catch (Exception e) {
                logger.warning("通知 " + entry.getKey() + " 传送失败时发生错误: " + e.getMessage());
            }
        }
    }
    
    /**
     * 重新加载兼容性设置
     */
    public void reloadCompatibility() {
        // 清除现有钩子
        pluginHooks.clear();
        
        // 重新加载设置
        loadCompatibilitySettings();
        
        // 重新初始化钩子
        initializePluginHooks();
        
        logger.info("兼容性设置已重新加载");
    }
    
    /**
     * 获取已加载的插件钩子列表
     * @return 插件钩子名称列表
     */
    public String[] getLoadedHooks() {
        return pluginHooks.keySet().toArray(new String[0]);
    }
    
    /**
     * 检查特定插件是否已加载钩子
     * @param pluginName 插件名称
     * @return 是否已加载
     */
    public boolean hasHook(String pluginName) {
        return pluginHooks.containsKey(pluginName);
    }
    
    /**
     * 获取兼容性统计信息
     * @return 统计信息
     */
    public CompatibilityStats getCompatibilityStats() {
        int loadedHooks = pluginHooks.size();
        boolean hooksEnabled = enablePluginHooks;
        boolean eventsEnabled = checkTeleportEvents;
        boolean regionProtectionEnabled = checkRegionProtection;
        
        return new CompatibilityStats(loadedHooks, hooksEnabled, eventsEnabled, regionProtectionEnabled);
    }
    
    /**
     * 兼容性统计信息类
     */
    public static class CompatibilityStats {
        private final int loadedHooks;
        private final boolean hooksEnabled;
        private final boolean eventsEnabled;
        private final boolean regionProtectionEnabled;
        
        public CompatibilityStats(int loadedHooks, boolean hooksEnabled, 
                                boolean eventsEnabled, boolean regionProtectionEnabled) {
            this.loadedHooks = loadedHooks;
            this.hooksEnabled = hooksEnabled;
            this.eventsEnabled = eventsEnabled;
            this.regionProtectionEnabled = regionProtectionEnabled;
        }
        
        public int getLoadedHooks() { return loadedHooks; }
        public boolean isHooksEnabled() { return hooksEnabled; }
        public boolean isEventsEnabled() { return eventsEnabled; }
        public boolean isRegionProtectionEnabled() { return regionProtectionEnabled; }
    }
}
