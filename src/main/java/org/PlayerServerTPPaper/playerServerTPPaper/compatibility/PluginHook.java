package org.PlayerServerTPPaper.playerServerTPPaper.compatibility;

import org.bukkit.Location;
import org.bukkit.entity.Player;

/**
 * 插件钩子接口
 * 定义与其他插件集成的标准接口
 */
public interface PluginHook {
    
    /**
     * 检查玩家是否可以传送到指定位置
     * @param player 玩家
     * @param targetLocation 目标位置
     * @return 是否可以传送
     */
    boolean canTeleport(Player player, Location targetLocation);
    
    /**
     * 传送成功后的回调
     * @param player 玩家
     * @param fromLocation 起始位置
     * @param toLocation 目标位置
     */
    default void onTeleportSuccess(Player player, Location fromLocation, Location toLocation) {
        // 默认实现为空
    }
    
    /**
     * 传送失败后的回调
     * @param player 玩家
     * @param fromLocation 起始位置
     * @param toLocation 目标位置
     */
    default void onTeleportFailed(Player player, Location fromLocation, Location toLocation) {
        // 默认实现为空
    }
    
    /**
     * 获取钩子名称
     * @return 钩子名称
     */
    String getHookName();
    
    /**
     * 检查钩子是否可用
     * @return 是否可用
     */
    boolean isAvailable();
}
