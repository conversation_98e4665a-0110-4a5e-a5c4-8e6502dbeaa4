package org.PlayerServerTPPaper.playerServerTPPaper.messaging;

import com.google.common.io.ByteArrayDataOutput;
import com.google.common.io.ByteStreams;
import org.bukkit.entity.Player;
import org.PlayerServerTPPaper.playerServerTPPaper.PlayerServerTPPaper;
import org.PlayerServerTPPaper.playerServerTPPaper.protocol.MessageType;
import org.PlayerServerTPPaper.playerServerTPPaper.protocol.ProtocolConstants;

import java.util.Collection;
import java.util.logging.Logger;

/**
 * 响应发送器
 * 负责向Velocity端发送响应消息
 */
public class ResponseSender {
    
    private final PlayerServerTPPaper plugin;
    private final Logger logger;
    
    public ResponseSender(PlayerServerTPPaper plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
    }
    
    /**
     * 发送传送响应消息
     * @param playerName 玩家名称
     * @param status 传送状态
     * @param message 响应消息
     * @param initiator 发起者
     * @return 是否发送成功
     */
    public boolean sendTeleportResponse(String playerName, String status, String message, String initiator) {
        String responseMessage = String.join(ProtocolConstants.MESSAGE_SEPARATOR,
            MessageType.TELEPORT_RESPONSE.getIdentifier(),
            playerName,
            status,
            message,
            initiator
        );
        
        return sendPluginMessage(responseMessage);
    }
    
    /**
     * 发送服务器列表响应
     * @param requester 请求者
     * @param serverList 服务器列表
     * @return 是否发送成功
     */
    public boolean sendServerListResponse(String requester, String[] serverList) {
        String servers = String.join(ProtocolConstants.SERVER_LIST_SEPARATOR, serverList);
        String responseMessage = String.join(ProtocolConstants.MESSAGE_SEPARATOR,
            MessageType.SERVER_LIST.getIdentifier(),
            requester,
            servers
        );
        
        return sendPluginMessage(responseMessage);
    }
    
    /**
     * 发送玩家状态响应
     * @param targetPlayer 目标玩家
     * @param status 玩家状态
     * @param serverName 服务器名称
     * @param requester 请求者
     * @return 是否发送成功
     */
    public boolean sendPlayerStatusResponse(String targetPlayer, String status, String serverName, String requester) {
        String responseMessage = String.join(ProtocolConstants.MESSAGE_SEPARATOR,
            MessageType.PLAYER_STATUS.getIdentifier(),
            targetPlayer,
            status,
            serverName,
            requester
        );
        
        return sendPluginMessage(responseMessage);
    }
    
    /**
     * 发送Plugin Message到Velocity
     * @param message 消息内容
     * @return 是否发送成功
     */
    private boolean sendPluginMessage(String message) {
        // 需要一个在线玩家来发送消息
        Player onlinePlayer = findOnlinePlayer();
        if (onlinePlayer == null) {
            logger.warning("没有在线玩家，无法发送Plugin Message响应");
            return false;
        }
        
        try {
            // 构建Plugin Message数据
            ByteArrayDataOutput out = ByteStreams.newDataOutput();
            out.writeUTF("Forward"); // BungeeCord Forward命令
            out.writeUTF("ALL"); // 发送到所有服务器（Velocity会处理）
            out.writeUTF(ProtocolConstants.SUB_CHANNEL); // 子通道
            
            // 构建实际消息数据
            ByteArrayDataOutput messageOut = ByteStreams.newDataOutput();
            messageOut.writeUTF(message);
            byte[] messageBytes = messageOut.toByteArray();
            
            out.writeShort(messageBytes.length);
            out.write(messageBytes);
            
            // 发送消息
            onlinePlayer.sendPluginMessage(plugin, ProtocolConstants.CHANNEL_NAME, out.toByteArray());
            
            // 调试日志
            if (plugin.getConfig().getBoolean("settings.debug-mode", false)) {
                logger.info("发送响应消息: " + message);
            }
            
            return true;
            
        } catch (Exception e) {
            logger.severe("发送Plugin Message响应时发生错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 查找一个在线玩家用于发送Plugin Message
     * @return 在线玩家（如果有的话）
     */
    private Player findOnlinePlayer() {
        Collection<? extends Player> onlinePlayers = plugin.getServer().getOnlinePlayers();
        return onlinePlayers.isEmpty() ? null : onlinePlayers.iterator().next();
    }
    
    /**
     * 发送自定义响应消息
     * @param messageType 消息类型
     * @param parts 消息部分
     * @return 是否发送成功
     */
    public boolean sendCustomResponse(MessageType messageType, String... parts) {
        if (messageType == null || parts == null) {
            logger.warning("发送自定义响应时参数无效");
            return false;
        }
        
        // 构建消息
        String[] messageParts = new String[parts.length + 1];
        messageParts[0] = messageType.getIdentifier();
        System.arraycopy(parts, 0, messageParts, 1, parts.length);
        
        String message = String.join(ProtocolConstants.MESSAGE_SEPARATOR, messageParts);
        return sendPluginMessage(message);
    }
    
    /**
     * 批量发送响应消息
     * @param messages 消息列表
     * @return 成功发送的消息数量
     */
    public int sendBatchResponses(String[] messages) {
        int successCount = 0;
        
        for (String message : messages) {
            if (message != null && !message.trim().isEmpty()) {
                if (sendPluginMessage(message)) {
                    successCount++;
                }
                
                // 在批量发送之间添加小延迟，避免消息过于频繁
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        logger.info("批量发送响应完成，成功发送 " + successCount + "/" + messages.length + " 条消息");
        return successCount;
    }
    
    /**
     * 发送错误响应
     * @param operation 操作类型
     * @param errorMessage 错误消息
     * @param requester 请求者
     * @return 是否发送成功
     */
    public boolean sendErrorResponse(String operation, String errorMessage, String requester) {
        String responseMessage = String.join(ProtocolConstants.MESSAGE_SEPARATOR,
            "ERROR",
            operation,
            errorMessage,
            requester,
            plugin.getServer().getName()
        );
        
        return sendPluginMessage(responseMessage);
    }
    
    /**
     * 发送心跳响应
     * @param requester 请求者
     * @return 是否发送成功
     */
    public boolean sendHeartbeatResponse(String requester) {
        String responseMessage = String.join(ProtocolConstants.MESSAGE_SEPARATOR,
            "HEARTBEAT",
            plugin.getServer().getName(),
            String.valueOf(plugin.getServer().getOnlinePlayers().size()),
            String.valueOf(System.currentTimeMillis()),
            requester
        );
        
        return sendPluginMessage(responseMessage);
    }
    
    /**
     * 检查响应发送器是否可用
     * @return 是否可用
     */
    public boolean isAvailable() {
        return findOnlinePlayer() != null;
    }
    
    /**
     * 获取响应发送器统计信息
     * @return 统计信息
     */
    public ResponseStats getResponseStats() {
        int onlinePlayerCount = plugin.getServer().getOnlinePlayers().size();
        boolean canSendMessages = onlinePlayerCount > 0;
        String serverName = plugin.getServer().getName();
        
        return new ResponseStats(onlinePlayerCount, canSendMessages, serverName);
    }
    
    /**
     * 响应统计信息类
     */
    public static class ResponseStats {
        private final int onlinePlayerCount;
        private final boolean canSendMessages;
        private final String serverName;
        
        public ResponseStats(int onlinePlayerCount, boolean canSendMessages, String serverName) {
            this.onlinePlayerCount = onlinePlayerCount;
            this.canSendMessages = canSendMessages;
            this.serverName = serverName;
        }
        
        public int getOnlinePlayerCount() {
            return onlinePlayerCount;
        }
        
        public boolean canSendMessages() {
            return canSendMessages;
        }
        
        public String getServerName() {
            return serverName;
        }
        
        @Override
        public String toString() {
            return String.format("ResponseStats{在线玩家: %d, 可发送消息: %s, 服务器: %s}", 
                onlinePlayerCount, canSendMessages, serverName);
        }
    }
}
