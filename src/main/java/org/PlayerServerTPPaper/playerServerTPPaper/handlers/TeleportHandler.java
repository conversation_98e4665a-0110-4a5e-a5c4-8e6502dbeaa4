package org.PlayerServerTPPaper.playerServerTPPaper.handlers;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.PlayerServerTPPaper.playerServerTPPaper.PlayerServerTPPaper;
import org.PlayerServerTPPaper.playerServerTPPaper.protocol.ProtocolConstants;
import org.PlayerServerTPPaper.playerServerTPPaper.safety.SafeLocationFinder;
import org.PlayerServerTPPaper.playerServerTPPaper.executors.TeleportExecutor;
import org.PlayerServerTPPaper.playerServerTPPaper.utils.MessageUtils;

import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

/**
 * 传送处理器
 * 负责处理传送请求的验证、预处理和执行
 */
public class TeleportHandler {
    
    private final PlayerServerTPPaper plugin;
    private final SafeLocationFinder safeLocationFinder;
    private final TeleportExecutor teleportExecutor;
    private final Logger logger;
    
    public TeleportHandler(PlayerServerTPPaper plugin, SafeLocationFinder safeLocationFinder, 
                          TeleportExecutor teleportExecutor) {
        this.plugin = plugin;
        this.safeLocationFinder = safeLocationFinder;
        this.teleportExecutor = teleportExecutor;
        this.logger = plugin.getLogger();
    }
    
    /**
     * 处理传送请求
     * @param targetPlayerName 要传送的玩家名称
     * @param targetServer 目标服务器（这里应该是当前服务器）
     * @param initiator 发起传送的玩家
     */
    public void handleTeleportRequest(String targetPlayerName, String targetServer, String initiator) {
        // 异步处理传送请求，避免阻塞主线程
        CompletableFuture.runAsync(() -> {
            processTeleportRequest(targetPlayerName, targetServer, initiator);
        }).exceptionally(throwable -> {
            logger.severe("处理传送请求时发生异常: " + throwable.getMessage());
            throwable.printStackTrace();
            
            // 发送失败响应
            sendTeleportResponse(targetPlayerName, ProtocolConstants.TeleportStatus.FAILED, 
                "处理传送请求时发生内部错误", initiator);
            return null;
        });
    }
    
    /**
     * 处理传送请求的核心逻辑
     */
    private void processTeleportRequest(String targetPlayerName, String targetServer, String initiator) {
        // 调试日志
        if (plugin.getConfig().getBoolean("settings.debug-mode", false)) {
            logger.info("开始处理传送请求 - 玩家: " + targetPlayerName + 
                       ", 服务器: " + targetServer + ", 发起者: " + initiator);
        }
        
        // 1. 验证玩家是否在线
        Player targetPlayer = Bukkit.getPlayer(targetPlayerName);
        if (targetPlayer == null) {
            logger.warning("玩家 " + targetPlayerName + " 不在线或不存在");
            sendTeleportResponse(targetPlayerName, ProtocolConstants.TeleportStatus.PLAYER_NOT_FOUND, 
                "玩家不在线或不存在", initiator);
            return;
        }
        
        // 2. 检查玩家是否已经在目标服务器
        String currentServerName = plugin.getServer().getName();
        if (currentServerName.equals(targetServer)) {
            logger.info("玩家 " + targetPlayerName + " 已经在目标服务器 " + targetServer);
            sendTeleportResponse(targetPlayerName, ProtocolConstants.TeleportStatus.SUCCESS, 
                "玩家已经在目标服务器", initiator);
            return;
        }
        
        // 3. 获取传送目标位置
        Location targetLocation = getDefaultTeleportLocation();
        if (targetLocation == null) {
            logger.severe("无法获取默认传送位置");
            sendTeleportResponse(targetPlayerName, ProtocolConstants.TeleportStatus.FAILED, 
                "无法获取传送位置", initiator);
            return;
        }
        
        // 4. 检查位置安全性（如果启用）
        if (plugin.getConfig().getBoolean("safety.check-safe-location", true)) {
            processSafeTeleport(targetPlayer, targetLocation, initiator);
        } else {
            // 直接传送
            executeDirectTeleport(targetPlayer, targetLocation, initiator);
        }
    }
    
    /**
     * 处理安全传送
     */
    private void processSafeTeleport(Player player, Location targetLocation, String initiator) {
        // 检查位置是否安全
        if (safeLocationFinder.isSafeLocation(targetLocation)) {
            // 位置安全，直接传送
            executeDirectTeleport(player, targetLocation, initiator);
        } else {
            // 位置不安全，寻找安全位置
            if (plugin.getConfig().getBoolean("safety.find-safe-location", true)) {
                findAndTeleportToSafeLocation(player, targetLocation, initiator);
            } else {
                // 不寻找安全位置，传送失败
                logger.warning("传送位置不安全，且未启用安全位置搜索");
                sendTeleportResponse(player.getName(), ProtocolConstants.TeleportStatus.UNSAFE_LOCATION, 
                    "传送位置不安全", initiator);
            }
        }
    }
    
    /**
     * 寻找安全位置并传送
     */
    private void findAndTeleportToSafeLocation(Player player, Location originalLocation, String initiator) {
        // 通知玩家正在寻找安全位置
        String searchingMessage = plugin.getConfig().getString("messages.unsafe-location", 
            "&c目标位置不安全，正在寻找安全位置...");
        player.sendMessage(MessageUtils.colorize(searchingMessage));
        
        // 异步寻找安全位置
        CompletableFuture<Location> safeFuture = safeLocationFinder.findSafeLocationAsync(originalLocation);
        
        safeFuture.thenAccept(safeLocation -> {
            // 回到主线程执行传送
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (safeLocation != null) {
                        String foundMessage = plugin.getConfig().getString("messages.safe-location-found", 
                            "&a已找到安全位置，正在传送...");
                        player.sendMessage(MessageUtils.colorize(foundMessage));
                        
                        executeDirectTeleport(player, safeLocation, initiator);
                    } else {
                        String notFoundMessage = plugin.getConfig().getString("messages.safe-location-not-found", 
                            "&c无法找到安全的传送位置！");
                        player.sendMessage(MessageUtils.colorize(notFoundMessage));
                        
                        sendTeleportResponse(player.getName(), ProtocolConstants.TeleportStatus.UNSAFE_LOCATION, 
                            "无法找到安全的传送位置", initiator);
                    }
                }
            }.runTask(plugin);
        }).exceptionally(throwable -> {
            logger.severe("寻找安全位置时发生错误: " + throwable.getMessage());
            
            // 回到主线程发送错误消息
            new BukkitRunnable() {
                @Override
                public void run() {
                    sendTeleportResponse(player.getName(), ProtocolConstants.TeleportStatus.FAILED, 
                        "寻找安全位置时发生错误", initiator);
                }
            }.runTask(plugin);
            
            return null;
        });
    }
    
    /**
     * 执行直接传送
     */
    private void executeDirectTeleport(Player player, Location targetLocation, String initiator) {
        // 回到主线程执行传送
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    // 使用传送执行器执行传送
                    boolean success = teleportExecutor.executeTeleport(player, targetLocation);
                    
                    if (success) {
                        String successMessage = plugin.getConfig().getString("messages.teleport-success", 
                            "&a传送成功！");
                        player.sendMessage(MessageUtils.colorize(successMessage));
                        
                        sendTeleportResponse(player.getName(), ProtocolConstants.TeleportStatus.SUCCESS, 
                            "传送成功", initiator);
                        
                        logger.info("玩家 " + player.getName() + " 传送成功");
                    } else {
                        String failedMessage = plugin.getConfig().getString("messages.teleport-failed", 
                            "&c传送失败：{reason}");
                        failedMessage = MessageUtils.replacePlaceholder(failedMessage, "reason", "未知错误");
                        player.sendMessage(MessageUtils.colorize(failedMessage));
                        
                        sendTeleportResponse(player.getName(), ProtocolConstants.TeleportStatus.FAILED, 
                            "传送执行失败", initiator);
                    }
                } catch (Exception e) {
                    logger.severe("执行传送时发生错误: " + e.getMessage());
                    e.printStackTrace();
                    
                    sendTeleportResponse(player.getName(), ProtocolConstants.TeleportStatus.FAILED, 
                        "传送执行时发生错误", initiator);
                }
            }
        }.runTask(plugin);
    }
    
    /**
     * 获取默认传送位置
     * @return 默认传送位置
     */
    private Location getDefaultTeleportLocation() {
        // 获取主世界的出生点作为默认传送位置
        World mainWorld = Bukkit.getWorlds().get(0);
        if (mainWorld != null) {
            return mainWorld.getSpawnLocation();
        }
        
        logger.warning("无法获取主世界，使用默认位置");
        return null;
    }
    
    /**
     * 发送传送响应消息
     * @param playerName 玩家名称
     * @param status 传送状态
     * @param message 响应消息
     * @param initiator 发起者
     */
    private void sendTeleportResponse(String playerName, String status, String message, String initiator) {
        try {
            // 获取响应发送器
            if (plugin.getResponseSender() != null) {
                boolean success = plugin.getResponseSender().sendTeleportResponse(playerName, status, message, initiator);

                if (!success) {
                    logger.warning("发送传送响应失败 - 玩家: " + playerName + ", 状态: " + status);
                }
            } else {
                logger.severe("响应发送器未初始化，无法发送传送响应");
            }

            // 调试日志
            if (plugin.getConfig().getBoolean("settings.debug-mode", false)) {
                logger.info("传送响应 - 玩家: " + playerName +
                           ", 状态: " + status +
                           ", 消息: " + message +
                           ", 发起者: " + initiator);
            }

        } catch (Exception e) {
            logger.severe("发送传送响应时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 获取传送处理统计信息
     * @return 统计信息
     */
    public String getHandlerStats() {
        return "传送处理器运行正常";
    }
}
