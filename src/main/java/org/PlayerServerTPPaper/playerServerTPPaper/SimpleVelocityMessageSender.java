package org.PlayerServerTPPaper.playerServerTPPaper;

import com.google.common.io.ByteArrayDataOutput;
import com.google.common.io.ByteStreams;
import org.bukkit.entity.Player;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.Collection;

/**
 * 简化版的Velocity消息发送器
 * 用于没有完整Paper端插件的服务器
 */
public class SimpleVelocityMessageSender {
    
    private final JavaPlugin plugin;
    private static final String CHANNEL_NAME = "playerservertp:main";
    private static final String MESSAGE_SEPARATOR = "|";
    
    public SimpleVelocityMessageSender(JavaPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 请求传送玩家
     */
    public boolean requestTeleport(String targetPlayer, String targetServer, String initiator) {
        String message = String.join(MESSAGE_SEPARATOR,
            "REQUEST_TELEPORT",
            targetPlayer,
            targetServer,
            initiator
        );
        
        return sendMessageToVelocity(message);
    }
    
    /**
     * 请求服务器列表
     */
    public boolean requestServerList(String requester) {
        String message = String.join(MESSAGE_SEPARATOR,
            "REQUEST_SERVER_LIST",
            requester
        );
        
        return sendMessageToVelocity(message);
    }
    
    /**
     * 向Velocity发送Plugin Message
     */
    private boolean sendMessageToVelocity(String message) {
        Player onlinePlayer = findOnlinePlayer();
        if (onlinePlayer == null) {
            plugin.getLogger().warning("No online players, cannot send Plugin Message to Velocity");
            return false;
        }
        
        try {
            ByteArrayDataOutput out = ByteStreams.newDataOutput();
            out.writeUTF(message);
            
            onlinePlayer.sendPluginMessage(plugin, CHANNEL_NAME, out.toByteArray());
            
            plugin.getLogger().info("Sent request message to Velocity: " + message);
            return true;
            
        } catch (Exception e) {
            plugin.getLogger().severe("Error sending Plugin Message to Velocity: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 查找一个在线玩家用于发送Plugin Message
     */
    private Player findOnlinePlayer() {
        Collection<? extends Player> onlinePlayers = plugin.getServer().getOnlinePlayers();
        return onlinePlayers.isEmpty() ? null : onlinePlayers.iterator().next();
    }
}
