package org.PlayerServerTPPaper.playerServerTPPaper.utils;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.ChatColor;

/**
 * 消息处理工具类
 * 提供消息格式化、颜色处理等功能
 */
public final class MessageUtils {
    
    private static final LegacyComponentSerializer LEGACY_SERIALIZER = 
        LegacyComponentSerializer.legacyAmpersand();
    
    /**
     * 将带有颜色代码的字符串转换为Component
     * @param message 带有&颜色代码的消息
     * @return 格式化的Component
     */
    public static Component colorize(String message) {
        if (message == null || message.isEmpty()) {
            return Component.empty();
        }
        return LEGACY_SERIALIZER.deserialize(message);
    }
    
    /**
     * 将带有颜色代码的字符串转换为传统的ChatColor格式
     * @param message 带有&颜色代码的消息
     * @return 格式化的字符串
     */
    public static String colorizeString(String message) {
        if (message == null || message.isEmpty()) {
            return "";
        }
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    
    /**
     * 替换消息中的占位符
     * @param message 原始消息
     * @param placeholder 占位符（不包含大括号）
     * @param replacement 替换内容
     * @return 替换后的消息
     */
    public static String replacePlaceholder(String message, String placeholder, String replacement) {
        if (message == null) return "";
        return message.replace("{" + placeholder + "}", replacement != null ? replacement : "");
    }
    
    /**
     * 替换多个占位符
     * @param message 原始消息
     * @param placeholders 占位符数组（偶数索引为占位符，奇数索引为替换内容）
     * @return 替换后的消息
     */
    public static String replacePlaceholders(String message, String... placeholders) {
        if (message == null) return "";
        if (placeholders.length % 2 != 0) {
            throw new IllegalArgumentException("占位符数组长度必须为偶数");
        }
        
        String result = message;
        for (int i = 0; i < placeholders.length; i += 2) {
            result = replacePlaceholder(result, placeholders[i], placeholders[i + 1]);
        }
        return result;
    }
    
    /**
     * 创建成功消息
     * @param message 消息内容
     * @return 绿色的成功消息Component
     */
    public static Component success(String message) {
        return Component.text(message, NamedTextColor.GREEN);
    }
    
    /**
     * 创建错误消息
     * @param message 消息内容
     * @return 红色的错误消息Component
     */
    public static Component error(String message) {
        return Component.text(message, NamedTextColor.RED);
    }
    
    /**
     * 创建警告消息
     * @param message 消息内容
     * @return 黄色的警告消息Component
     */
    public static Component warning(String message) {
        return Component.text(message, NamedTextColor.YELLOW);
    }
    
    /**
     * 创建信息消息
     * @param message 消息内容
     * @return 蓝色的信息消息Component
     */
    public static Component info(String message) {
        return Component.text(message, NamedTextColor.AQUA);
    }
    
    /**
     * 创建标题消息
     * @param message 消息内容
     * @return 金色加粗的标题消息Component
     */
    public static Component title(String message) {
        return Component.text(message, NamedTextColor.GOLD, TextDecoration.BOLD);
    }
    
    /**
     * 格式化时间（秒）为可读字符串
     * @param seconds 秒数
     * @return 格式化的时间字符串
     */
    public static String formatTime(long seconds) {
        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            long minutes = seconds / 60;
            long remainingSeconds = seconds % 60;
            return minutes + "分" + (remainingSeconds > 0 ? remainingSeconds + "秒" : "");
        } else {
            long hours = seconds / 3600;
            long remainingMinutes = (seconds % 3600) / 60;
            return hours + "小时" + (remainingMinutes > 0 ? remainingMinutes + "分" : "");
        }
    }
    
    // 防止实例化
    private MessageUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
