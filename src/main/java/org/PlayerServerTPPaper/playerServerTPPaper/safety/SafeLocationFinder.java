package org.PlayerServerTPPaper.playerServerTPPaper.safety;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.scheduler.BukkitRunnable;
import org.PlayerServerTPPaper.playerServerTPPaper.PlayerServerTPPaper;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

/**
 * 安全位置检测器
 * 负责检测传送位置的安全性并寻找安全的替代位置
 */
public class SafeLocationFinder {
    
    private final PlayerServerTPPaper plugin;
    private final Logger logger;
    
    // 危险方块集合
    private final Set<Material> unsafeBlocks;
    
    public SafeLocationFinder(PlayerServerTPPaper plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.unsafeBlocks = new HashSet<>();
        
        // 初始化危险方块列表
        loadUnsafeBlocks();
    }
    
    /**
     * 从配置文件加载危险方块列表
     */
    private void loadUnsafeBlocks() {
        unsafeBlocks.clear();
        
        List<String> unsafeBlockNames = plugin.getConfig().getStringList("safety.unsafe-blocks");
        if (unsafeBlockNames.isEmpty()) {
            // 默认危险方块
            unsafeBlockNames = List.of("LAVA", "FIRE", "CACTUS", "MAGMA_BLOCK", "SWEET_BERRY_BUSH");
        }
        
        for (String blockName : unsafeBlockNames) {
            try {
                Material material = Material.valueOf(blockName.toUpperCase());
                unsafeBlocks.add(material);
            } catch (IllegalArgumentException e) {
                logger.warning("未知的方块类型: " + blockName);
            }
        }
        
        logger.info("已加载 " + unsafeBlocks.size() + " 种危险方块类型");
    }
    
    /**
     * 检查位置是否安全
     * @param location 要检查的位置
     * @return 是否安全
     */
    public boolean isSafeLocation(Location location) {
        if (location == null || location.getWorld() == null) {
            return false;
        }
        
        World world = location.getWorld();
        int x = location.getBlockX();
        int y = location.getBlockY();
        int z = location.getBlockZ();
        
        // 检查Y坐标是否在有效范围内
        if (y < world.getMinHeight() || y > world.getMaxHeight() - 2) {
            return false;
        }
        
        // 检查脚下的方块
        Block groundBlock = world.getBlockAt(x, y - 1, z);
        if (groundBlock.getType() == Material.AIR || groundBlock.getType() == Material.VOID_AIR) {
            return false; // 没有地面支撑
        }
        
        // 检查脚下是否是危险方块
        if (unsafeBlocks.contains(groundBlock.getType())) {
            return false;
        }
        
        // 检查玩家站立位置（脚部）
        Block feetBlock = world.getBlockAt(x, y, z);
        if (isSolidBlock(feetBlock) || unsafeBlocks.contains(feetBlock.getType())) {
            return false;
        }
        
        // 检查玩家头部位置
        Block headBlock = world.getBlockAt(x, y + 1, z);
        if (isSolidBlock(headBlock) || unsafeBlocks.contains(headBlock.getType())) {
            return false;
        }
        
        // 检查周围是否有危险方块
        if (hasDangerousBlocksNearby(location)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 异步寻找安全位置
     * @param originalLocation 原始位置
     * @return 安全位置的CompletableFuture
     */
    public CompletableFuture<Location> findSafeLocationAsync(Location originalLocation) {
        CompletableFuture<Location> future = new CompletableFuture<>();
        
        // 在异步任务中执行搜索
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    Location safeLocation = findSafeLocation(originalLocation);
                    future.complete(safeLocation);
                } catch (Exception e) {
                    logger.severe("寻找安全位置时发生错误: " + e.getMessage());
                    future.completeExceptionally(e);
                }
            }
        }.runTaskAsynchronously(plugin);
        
        return future;
    }
    
    /**
     * 寻找安全位置
     * @param originalLocation 原始位置
     * @return 安全位置，如果找不到则返回null
     */
    public Location findSafeLocation(Location originalLocation) {
        if (originalLocation == null || originalLocation.getWorld() == null) {
            return null;
        }
        
        World world = originalLocation.getWorld();
        int centerX = originalLocation.getBlockX();
        int centerY = originalLocation.getBlockY();
        int centerZ = originalLocation.getBlockZ();
        
        int searchRadius = plugin.getConfig().getInt("safety.search-radius", 5);
        int maxHeightDiff = plugin.getConfig().getInt("safety.max-height-difference", 10);
        
        // 首先检查原始位置
        if (isSafeLocation(originalLocation)) {
            return originalLocation;
        }
        
        // 螺旋搜索安全位置
        for (int radius = 1; radius <= searchRadius; radius++) {
            for (int dx = -radius; dx <= radius; dx++) {
                for (int dz = -radius; dz <= radius; dz++) {
                    // 只检查当前半径边界上的点
                    if (Math.abs(dx) != radius && Math.abs(dz) != radius) {
                        continue;
                    }
                    
                    int x = centerX + dx;
                    int z = centerZ + dz;
                    
                    // 在垂直方向搜索
                    Location safeLocation = findSafeLocationVertically(world, x, centerY, z, maxHeightDiff);
                    if (safeLocation != null) {
                        return safeLocation;
                    }
                }
            }
        }
        
        // 如果找不到安全位置，尝试世界出生点
        Location spawnLocation = world.getSpawnLocation();
        if (isSafeLocation(spawnLocation)) {
            logger.info("使用世界出生点作为安全位置");
            return spawnLocation;
        }
        
        logger.warning("无法找到安全的传送位置");
        return null;
    }
    
    /**
     * 在垂直方向寻找安全位置
     * @param world 世界
     * @param x X坐标
     * @param centerY 中心Y坐标
     * @param z Z坐标
     * @param maxHeightDiff 最大高度差
     * @return 安全位置，如果找不到则返回null
     */
    private Location findSafeLocationVertically(World world, int x, int centerY, int z, int maxHeightDiff) {
        // 先检查中心高度
        Location centerLocation = new Location(world, x + 0.5, centerY, z + 0.5);
        if (isSafeLocation(centerLocation)) {
            return centerLocation;
        }
        
        // 向上和向下搜索
        for (int dy = 1; dy <= maxHeightDiff; dy++) {
            // 向上搜索
            int upperY = centerY + dy;
            if (upperY <= world.getMaxHeight() - 2) {
                Location upperLocation = new Location(world, x + 0.5, upperY, z + 0.5);
                if (isSafeLocation(upperLocation)) {
                    return upperLocation;
                }
            }
            
            // 向下搜索
            int lowerY = centerY - dy;
            if (lowerY >= world.getMinHeight()) {
                Location lowerLocation = new Location(world, x + 0.5, lowerY, z + 0.5);
                if (isSafeLocation(lowerLocation)) {
                    return lowerLocation;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 检查方块是否是固体方块
     * @param block 方块
     * @return 是否是固体方块
     */
    private boolean isSolidBlock(Block block) {
        Material type = block.getType();
        return type.isSolid() && type != Material.BARRIER;
    }
    
    /**
     * 检查位置附近是否有危险方块
     * @param location 位置
     * @return 是否有危险方块
     */
    private boolean hasDangerousBlocksNearby(Location location) {
        World world = location.getWorld();
        int x = location.getBlockX();
        int y = location.getBlockY();
        int z = location.getBlockZ();
        
        // 检查周围1格范围内的方块
        for (int dx = -1; dx <= 1; dx++) {
            for (int dy = -1; dy <= 1; dy++) {
                for (int dz = -1; dz <= 1; dz++) {
                    Block block = world.getBlockAt(x + dx, y + dy, z + dz);
                    if (unsafeBlocks.contains(block.getType())) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * 重新加载安全设置
     */
    public void reloadSafetySettings() {
        loadUnsafeBlocks();
        logger.info("安全设置已重新加载");
    }
    
    /**
     * 获取危险方块列表
     * @return 危险方块集合
     */
    public Set<Material> getUnsafeBlocks() {
        return new HashSet<>(unsafeBlocks);
    }
    
    /**
     * 添加危险方块
     * @param material 方块材质
     */
    public void addUnsafeBlock(Material material) {
        unsafeBlocks.add(material);
    }
    
    /**
     * 移除危险方块
     * @param material 方块材质
     */
    public void removeUnsafeBlock(Material material) {
        unsafeBlocks.remove(material);
    }
    
    /**
     * 获取安全检查统计信息
     * @return 统计信息
     */
    public SafetyStats getSafetyStats() {
        int unsafeBlockCount = unsafeBlocks.size();
        int searchRadius = plugin.getConfig().getInt("safety.search-radius", 5);
        int maxHeightDiff = plugin.getConfig().getInt("safety.max-height-difference", 10);
        boolean safetyEnabled = plugin.getConfig().getBoolean("safety.check-safe-location", true);
        
        return new SafetyStats(unsafeBlockCount, searchRadius, maxHeightDiff, safetyEnabled);
    }
    
    /**
     * 安全统计信息类
     */
    public static class SafetyStats {
        private final int unsafeBlockCount;
        private final int searchRadius;
        private final int maxHeightDiff;
        private final boolean safetyEnabled;
        
        public SafetyStats(int unsafeBlockCount, int searchRadius, int maxHeightDiff, boolean safetyEnabled) {
            this.unsafeBlockCount = unsafeBlockCount;
            this.searchRadius = searchRadius;
            this.maxHeightDiff = maxHeightDiff;
            this.safetyEnabled = safetyEnabled;
        }
        
        public int getUnsafeBlockCount() { return unsafeBlockCount; }
        public int getSearchRadius() { return searchRadius; }
        public int getMaxHeightDiff() { return maxHeightDiff; }
        public boolean isSafetyEnabled() { return safetyEnabled; }
    }
}
