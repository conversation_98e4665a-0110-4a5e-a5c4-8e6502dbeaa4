package org.PlayerServerTPPaper.playerServerTPPaper;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 简化版的跨服传送指令
 * 用于没有完整Paper端插件的服务器
 * 只负责转发指令到Velocity端处理
 */
public class SimpleServerTeleportCommand implements CommandExecutor, TabCompleter {
    
    private final JavaPlugin plugin;
    private final SimpleVelocityMessageSender messageSender;
    
    public SimpleServerTeleportCommand(JavaPlugin plugin) {
        this.plugin = plugin;
        this.messageSender = new SimpleVelocityMessageSender(plugin);
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c只有玩家可以使用此指令！");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length == 0) {
            showHelp(player);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "help":
                showHelp(player);
                break;
            case "list":
                handleServerList(player);
                break;
            case "tp":
                if (args.length < 2) {
                    player.sendMessage("§c用法: /stp tp <服务器>");
                    return true;
                }
                handleSelfTeleport(player, args[1]);
                break;
            case "tpto":
                if (args.length < 3) {
                    player.sendMessage("§c用法: /stp tpto <玩家> <服务器>");
                    return true;
                }
                handleOtherTeleport(player, args[1], args[2]);
                break;
            default:
                showHelp(player);
                break;
        }
        
        return true;
    }
    
    private void showHelp(Player player) {
        player.sendMessage("§6=== PlayerServerTP 帮助 ===");
        player.sendMessage("§e/stp tp <服务器> §7- 传送到指定服务器");
        player.sendMessage("§e/stp list §7- 查看可用服务器列表");
        
        if (player.hasPermission("playerservertp.teleport.others")) {
            player.sendMessage("§e/stp tpto <玩家> <服务器> §7- 传送玩家到指定服务器");
        }
    }
    
    private void handleServerList(Player player) {
        if (!player.hasPermission("playerservertp.list")) {
            player.sendMessage("§c你没有权限使用此指令！");
            return;
        }
        
        // 发送请求到Velocity
        boolean success = messageSender.requestServerList(player.getName());
        if (success) {
            player.sendMessage("§e正在获取服务器列表...");
        } else {
            player.sendMessage("§c获取服务器列表失败");
        }
    }
    
    private void handleSelfTeleport(Player player, String targetServer) {
        if (!player.hasPermission("playerservertp.teleport.self")) {
            player.sendMessage("§c你没有权限使用此指令！");
            return;
        }
        
        // 发送传送请求到Velocity
        boolean success = messageSender.requestTeleport(player.getName(), targetServer, player.getName());
        if (success) {
            player.sendMessage("§e正在传送到 " + targetServer + "...");
        } else {
            player.sendMessage("§c传送请求失败");
        }
    }
    
    private void handleOtherTeleport(Player player, String targetPlayer, String targetServer) {
        if (!player.hasPermission("playerservertp.teleport.others")) {
            player.sendMessage("§c你没有权限使用此指令！");
            return;
        }
        
        // 发送传送请求到Velocity
        boolean success = messageSender.requestTeleport(targetPlayer, targetServer, player.getName());
        if (success) {
            player.sendMessage("§e正在传送 " + targetPlayer + " 到 " + targetServer + "...");
        } else {
            player.sendMessage("§c传送请求失败");
        }
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> suggestions = new ArrayList<>();
        
        if (args.length == 1) {
            String input = args[0].toLowerCase();
            
            if ("help".startsWith(input)) suggestions.add("help");
            if ("list".startsWith(input)) suggestions.add("list");
            if ("tp".startsWith(input)) suggestions.add("tp");
            if (sender.hasPermission("playerservertp.teleport.others") && "tpto".startsWith(input)) {
                suggestions.add("tpto");
            }
            
        } else if (args.length == 2) {
            String subCommand = args[0].toLowerCase();
            String input = args[1].toLowerCase();
            
            if ("tp".equals(subCommand)) {
                // 简单的服务器名称建议
                List<String> servers = Arrays.asList("lobby", "survival", "creative", "minigames");
                for (String server : servers) {
                    if (server.toLowerCase().startsWith(input)) {
                        suggestions.add(server);
                    }
                }
            } else if ("tpto".equals(subCommand) && sender.hasPermission("playerservertp.teleport.others")) {
                // 在线玩家建议
                plugin.getServer().getOnlinePlayers().forEach(player -> {
                    if (player.getName().toLowerCase().startsWith(input)) {
                        suggestions.add(player.getName());
                    }
                });
            }
            
        } else if (args.length == 3 && "tpto".equals(args[0].toLowerCase()) && 
                   sender.hasPermission("playerservertp.teleport.others")) {
            String input = args[2].toLowerCase();
            List<String> servers = Arrays.asList("lobby", "survival", "creative", "minigames");
            for (String server : servers) {
                if (server.toLowerCase().startsWith(input)) {
                    suggestions.add(server);
                }
            }
        }
        
        return suggestions;
    }
}
