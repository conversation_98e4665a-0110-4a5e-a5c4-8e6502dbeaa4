package org.PlayerServerTPPaper.playerServerTPPaper.executors;

import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerTeleportEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.PlayerServerTPPaper.playerServerTPPaper.PlayerServerTPPaper;
import org.PlayerServerTPPaper.playerServerTPPaper.compatibility.CompatibilityManager;

import java.util.logging.Logger;

/**
 * 传送执行器
 * 负责执行实际的传送操作，包括特效和兼容性处理
 */
public class TeleportExecutor {
    
    private final PlayerServerTPPaper plugin;
    private final CompatibilityManager compatibilityManager;
    private final Logger logger;
    
    public TeleportExecutor(PlayerServerTPPaper plugin, CompatibilityManager compatibilityManager) {
        this.plugin = plugin;
        this.compatibilityManager = compatibilityManager;
        this.logger = plugin.getLogger();
    }
    
    /**
     * 执行传送操作
     * @param player 要传送的玩家
     * @param targetLocation 目标位置
     * @return 是否传送成功
     */
    public boolean executeTeleport(Player player, Location targetLocation) {
        if (player == null || targetLocation == null) {
            logger.warning("传送参数无效：玩家或目标位置为null");
            return false;
        }
        
        try {
            // 1. 兼容性检查
            if (!compatibilityManager.canTeleport(player, targetLocation)) {
                logger.info("兼容性检查失败，传送被阻止");
                return false;
            }
            
            // 2. 播放传送前特效
            playBeforeTeleportEffects(player);
            
            // 3. 获取传送延迟
            long delay = plugin.getConfig().getLong("settings.teleport-delay", 100);
            
            if (delay > 0) {
                // 延迟传送
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        performTeleport(player, targetLocation);
                    }
                }.runTaskLater(plugin, delay / 50); // 转换为tick（1秒=20tick）
            } else {
                // 立即传送
                performTeleport(player, targetLocation);
            }
            
            return true;
            
        } catch (Exception e) {
            logger.severe("执行传送时发生错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 执行实际的传送操作
     * @param player 玩家
     * @param targetLocation 目标位置
     */
    private void performTeleport(Player player, Location targetLocation) {
        try {
            // 确保在主线程中执行
            if (!plugin.getServer().isPrimaryThread()) {
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        performTeleport(player, targetLocation);
                    }
                }.runTask(plugin);
                return;
            }
            
            // 检查玩家是否仍然在线
            if (!player.isOnline()) {
                logger.warning("玩家 " + player.getName() + " 已离线，取消传送");
                return;
            }
            
            // 记录传送前位置（用于调试）
            Location fromLocation = player.getLocation();
            
            // 执行传送
            boolean success = player.teleport(targetLocation, PlayerTeleportEvent.TeleportCause.PLUGIN);
            
            if (success) {
                // 传送成功，播放特效
                playAfterTeleportEffects(player);
                
                // 通知兼容性管理器
                compatibilityManager.onTeleportSuccess(player, fromLocation, targetLocation);
                
                // 调试日志
                if (plugin.getConfig().getBoolean("settings.debug-mode", false)) {
                    logger.info("玩家 " + player.getName() + " 传送成功：" + 
                               formatLocation(fromLocation) + " -> " + formatLocation(targetLocation));
                }
            } else {
                logger.warning("玩家 " + player.getName() + " 传送失败");
                
                // 通知兼容性管理器传送失败
                compatibilityManager.onTeleportFailed(player, fromLocation, targetLocation);
            }
            
        } catch (Exception e) {
            logger.severe("执行传送时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 播放传送前特效
     * @param player 玩家
     */
    private void playBeforeTeleportEffects(Player player) {
        if (!plugin.getConfig().getBoolean("effects.enable-effects", true)) {
            return;
        }
        
        try {
            Location location = player.getLocation();
            
            // 播放粒子特效
            String particleType = plugin.getConfig().getString("effects.before-teleport.particles.type", "PORTAL");
            int particleCount = plugin.getConfig().getInt("effects.before-teleport.particles.count", 20);
            
            try {
                Particle particle = Particle.valueOf(particleType.toUpperCase());
                location.getWorld().spawnParticle(particle, location.add(0, 1, 0), particleCount, 0.5, 1, 0.5, 0.1);
            } catch (IllegalArgumentException e) {
                logger.warning("未知的粒子类型: " + particleType);
            }
            
            // 播放声音特效
            String soundType = plugin.getConfig().getString("effects.before-teleport.sound.type", "ENTITY_ENDERMAN_TELEPORT");
            float volume = (float) plugin.getConfig().getDouble("effects.before-teleport.sound.volume", 1.0);
            float pitch = (float) plugin.getConfig().getDouble("effects.before-teleport.sound.pitch", 1.0);
            
            try {
                Sound sound = Sound.valueOf(soundType.toUpperCase());
                player.playSound(location, sound, volume, pitch);
            } catch (IllegalArgumentException e) {
                logger.warning("未知的声音类型: " + soundType);
            }
            
        } catch (Exception e) {
            logger.warning("播放传送前特效时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 播放传送后特效
     * @param player 玩家
     */
    private void playAfterTeleportEffects(Player player) {
        if (!plugin.getConfig().getBoolean("effects.enable-effects", true)) {
            return;
        }
        
        try {
            Location location = player.getLocation();
            
            // 播放粒子特效
            String particleType = plugin.getConfig().getString("effects.after-teleport.particles.type", "PORTAL");
            int particleCount = plugin.getConfig().getInt("effects.after-teleport.particles.count", 30);
            
            try {
                Particle particle = Particle.valueOf(particleType.toUpperCase());
                location.getWorld().spawnParticle(particle, location.add(0, 1, 0), particleCount, 0.5, 1, 0.5, 0.1);
            } catch (IllegalArgumentException e) {
                logger.warning("未知的粒子类型: " + particleType);
            }
            
            // 播放声音特效
            String soundType = plugin.getConfig().getString("effects.after-teleport.sound.type", "ENTITY_ENDERMAN_TELEPORT");
            float volume = (float) plugin.getConfig().getDouble("effects.after-teleport.sound.volume", 1.0);
            float pitch = (float) plugin.getConfig().getDouble("effects.after-teleport.sound.pitch", 1.2);
            
            try {
                Sound sound = Sound.valueOf(soundType.toUpperCase());
                player.playSound(location, sound, volume, pitch);
            } catch (IllegalArgumentException e) {
                logger.warning("未知的声音类型: " + soundType);
            }
            
        } catch (Exception e) {
            logger.warning("播放传送后特效时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 异步执行传送（用于需要等待的情况）
     * @param player 玩家
     * @param targetLocation 目标位置
     * @param delayTicks 延迟tick数
     * @return 是否成功安排传送
     */
    public boolean executeTeleportAsync(Player player, Location targetLocation, long delayTicks) {
        if (player == null || targetLocation == null) {
            return false;
        }
        
        try {
            new BukkitRunnable() {
                @Override
                public void run() {
                    executeTeleport(player, targetLocation);
                }
            }.runTaskLater(plugin, delayTicks);
            
            return true;
        } catch (Exception e) {
            logger.severe("安排异步传送时发生错误: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 批量传送多个玩家
     * @param players 玩家数组
     * @param targetLocation 目标位置
     * @return 成功传送的玩家数量
     */
    public int executeBatchTeleport(Player[] players, Location targetLocation) {
        int successCount = 0;
        
        for (Player player : players) {
            if (player != null && player.isOnline()) {
                if (executeTeleport(player, targetLocation)) {
                    successCount++;
                }
                
                // 在批量传送之间添加小延迟，避免服务器压力
                try {
                    Thread.sleep(50);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        logger.info("批量传送完成，成功传送 " + successCount + "/" + players.length + " 个玩家");
        return successCount;
    }
    
    /**
     * 格式化位置信息用于日志
     * @param location 位置
     * @return 格式化的位置字符串
     */
    private String formatLocation(Location location) {
        if (location == null) {
            return "null";
        }
        
        return String.format("%s(%.1f, %.1f, %.1f)", 
            location.getWorld() != null ? location.getWorld().getName() : "unknown",
            location.getX(), location.getY(), location.getZ());
    }
    
    /**
     * 检查传送执行器是否正常工作
     * @return 是否正常
     */
    public boolean isHealthy() {
        try {
            // 检查配置是否加载正常
            boolean effectsEnabled = plugin.getConfig().getBoolean("effects.enable-effects", true);
            long teleportDelay = plugin.getConfig().getLong("settings.teleport-delay", 100);
            
            // 检查兼容性管理器是否正常
            boolean compatibilityOk = compatibilityManager != null;
            
            return compatibilityOk;
        } catch (Exception e) {
            logger.warning("传送执行器健康检查失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取传送执行器统计信息
     * @return 统计信息
     */
    public String getExecutorStats() {
        boolean effectsEnabled = plugin.getConfig().getBoolean("effects.enable-effects", true);
        long teleportDelay = plugin.getConfig().getLong("settings.teleport-delay", 100);
        boolean healthy = isHealthy();
        
        return String.format("传送执行器状态 - 特效: %s, 延迟: %dms, 健康: %s", 
            effectsEnabled ? "启用" : "禁用", teleportDelay, healthy ? "正常" : "异常");
    }
}
