package org.PlayerServerTPPaper.playerServerTPPaper.protocol;

/**
 * 定义Plugin Messaging协议中使用的消息类型
 * 用于Velocity和Paper端之间的通信
 */
public enum MessageType {
    /**
     * 传送玩家到指定服务器
     * 格式: TELEPORT_PLAYER|玩家名|目标服务器|发起者
     */
    TELEPORT_PLAYER("TELEPORT_PLAYER"),
    
    /**
     * 传送响应消息
     * 格式: TELEPORT_RESPONSE|玩家名|状态|消息
     */
    TELEPORT_RESPONSE("TELEPORT_RESPONSE"),
    
    /**
     * 获取服务器列表
     * 格式: GET_SERVERS
     */
    GET_SERVERS("GET_SERVERS"),
    
    /**
     * 服务器列表响应
     * 格式: SERVER_LIST|服务器1,服务器2,服务器3
     */
    SERVER_LIST("SERVER_LIST"),
    
    /**
     * 检查玩家是否在线
     * 格式: CHECK_PLAYER|玩家名
     */
    CHECK_PLAYER("CHECK_PLAYER"),
    
    /**
     * 玩家在线状态响应
     * 格式: PLAYER_STATUS|玩家名|在线状态|所在服务器
     */
    PLAYER_STATUS("PLAYER_STATUS"),

    /**
     * 完整服务器列表（用于配置更新）
     * 格式: FULL_SERVER_LIST|请求者|服务器1,服务器2,服务器3
     */
    FULL_SERVER_LIST("FULL_SERVER_LIST");
    
    private final String identifier;
    
    MessageType(String identifier) {
        this.identifier = identifier;
    }
    
    public String getIdentifier() {
        return identifier;
    }
    
    /**
     * 根据标识符获取消息类型
     * @param identifier 消息标识符
     * @return 对应的消息类型，如果不存在则返回null
     */
    public static MessageType fromIdentifier(String identifier) {
        for (MessageType type : values()) {
            if (type.identifier.equals(identifier)) {
                return type;
            }
        }
        return null;
    }
}
