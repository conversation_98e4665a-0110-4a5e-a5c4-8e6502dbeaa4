# PlayerServerTP Velocity端配置文件
# 跨服传送插件配置

# 基础设置
settings:
  # 是否启用调试模式
  debug-mode: false
  
  # 传送冷却时间（秒）
  cooldown-time: 5
  
  # 是否启用安全传送（检查目标位置安全性）
  safe-teleport: true

# 服务器配置
servers:
  # 启用的服务器列表（玩家可以传送到的服务器）
  enabled-servers:
    - "lobby"
    - "survival"
    - "creative"
    - "minigames"
  
  # 服务器别名（可选，用于简化服务器名称）
  aliases:
    "hub": "lobby"
    "surv": "survival"
    "crea": "creative"
    "games": "minigames"
  
  # 服务器显示名称（用于消息显示）
  display-names:
    "lobby": "大厅服务器"
    "survival": "生存服务器"
    "creative": "创造服务器"
    "minigames": "小游戏服务器"

# 权限设置
permissions:
  # 是否启用权限检查
  enable-permission-check: true
  
  # 默认权限（所有玩家都有的权限）
  default-permissions:
    - "playerservertp.teleport.self"
    - "playerservertp.list"

# 消息配置
messages:
  # 成功消息
  teleport-success: "&a成功传送到服务器 &e{server}&a！"
  teleport-others-success: "&a成功将玩家 &e{player} &a传送到服务器 &e{server}&a！"

  # 传送过程消息
  teleport-preparing: "&e正在传送到服务器 &a{server}&e..."
  teleport-direct: "&a使用直接传送到服务器 &e{server}&a！"
  
  # 错误消息
  player-not-found: "&c玩家 &e{player} &c不存在或不在线！"
  server-not-found: "&c服务器 &e{server} &c不存在或未启用！"
  no-permission: "&c你没有权限执行此操作！"
  cooldown-active: "&c请等待 &e{time} &c秒后再次使用传送！"
  unsafe-location: "&c目标位置不安全，传送已取消！"
  
  # 帮助消息
  help-header: "&6=== PlayerServerTP 帮助 ==="
  help-stp: "&e/stp <服务器> &7- 传送到指定服务器"
  help-stp-player: "&e/stp <玩家> <服务器> &7- 传送玩家到指定服务器"
  help-stp-list: "&e/stp list &7- 查看可用服务器列表"
  
  # 服务器列表
  server-list-header: "&6=== 可用服务器列表 ==="
  server-list-item: "&e{server} &7- &f{display-name}"
  server-list-empty: "&c没有可用的服务器！"

# 指令设置
commands:
  # 主指令别名
  aliases:
    - "stp"
    - "servertp"
    - "跨服传送"
